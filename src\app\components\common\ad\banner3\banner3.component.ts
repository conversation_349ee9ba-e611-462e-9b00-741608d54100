import { isPlatformBrowser } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, Inject, OnD<PERSON>roy, OnInit, PLATFORM_ID, Renderer2, ViewChild } from '@angular/core';

@Component({
    selector: '[app-banner3]',
    templateUrl: './banner3.component.html',
    styleUrl: './banner3.component.scss',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Banner3Component implements OnInit, OnDestroy {

  @ViewChild('banner3', { static: true }) adContainer!: ElementRef;
  constructor(
    private renderer: Renderer2,
    private el: ElementRef,
    @Inject(PLATFORM_ID) private platformId: object) { }

  ngOnInit() {
    if ((isPlatformBrowser(this.platformId))) {
      this.loadAdScript('//chaseherbalpasty.com/lv/esnk/2051270/code.js', '__clb-2051270');
      this.loadAdScript('//chaseherbalpasty.com/lv/esnk/2051365/code.js', '__clb-2051365');
    }
  }
  ngOnDestroy() {
    // Xóa các script tìm được
    const adScripts = Array.from(document.querySelectorAll('script'))
      .filter(script =>
        script.src.includes('2051270') || script.src.includes('2051365') // Đường dẫn script quảng cáo
      );
    adScripts.forEach(script => script.remove());
  }
  // Phương thức để tải script
  private loadAdScript(src: string, className: string) {

    const script = this.renderer.createElement('script');
    script.type = 'text/javascript';
    script.src = src;
    script.async = true;
    script.dataset.cfasync = 'false';
    script.className = className;
    this.renderer.appendChild(this.adContainer.nativeElement, script);
  }

}
