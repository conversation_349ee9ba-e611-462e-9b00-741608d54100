<section class="container mx-auto px-3 py-8">
  <div class="bg-white/80 dark:bg-zinc-900/60 rounded-xl border border-zinc-200/60 dark:border-zinc-800 p-6">
    <h1 class="text-2xl font-bold mb-4"><PERSON><PERSON>u hỏi thường gặp</h1>

    <div class="grid md:grid-cols-2 gap-6">
      <div>
        <h2 class="font-semibold mb-2">Tài kho<PERSON> & đ<PERSON>ng bộ</h2>
        <div class="divide-y divide-zinc-200/50 dark:divide-zinc-800">
          <details class="py-3 group">
            <summary class="font-medium cursor-pointer flex items-center justify-between">
              Đăng ký tài khoản có lợi ích gì?
              <span class="text-sm opacity-70 group-open:rotate-180 transition">⌄</span>
            </summary>
            <p class="mt-2 text-sm opacity-80"><PERSON><PERSON><PERSON> c<PERSON> thể theo dõi truyện yê<PERSON> thích, đồ<PERSON> bộ lịch sử giữa thiết bị, nhận gợi ý phù hợp và bình luận.</p>
          </details>
          <details class="py-3 group">
            <summary class="font-medium cursor-pointer flex items-center justify-between">
              Đồng bộ dữ liệu hoạt động thế nào?
              <span class="text-sm opacity-70 group-open:rotate-180 transition">⌄</span>
            </summary>
            <p class="mt-2 text-sm opacity-80">Tại trang “Đồng bộ truyện”, bạn có thể nhập dữ liệu theo định dạng hỗ trợ hoặc dùng công cụ import tự động (khi khả dụng).</p>
          </details>
        </div>
      </div>

      <div>
        <h2 class="font-semibold mb-2">Tìm kiếm & đọc truyện</h2>
        <div class="divide-y divide-zinc-200/50 dark:divide-zinc-800">
          <details class="py-3 group">
            <summary class="font-medium cursor-pointer flex items-center justify-between">
              Làm sao để tìm truyện phù hợp?
              <span class="text-sm opacity-70 group-open:rotate-180 transition">⌄</span>
            </summary>
            <p class="mt-2 text-sm opacity-80">Dùng ô tìm kiếm, lọc theo thể loại tại trang “Thể loại”, hoặc xem các danh mục như “Truyện Hot”, “Xếp hạng”.</p>
          </details>
          <details class="py-3 group">
            <summary class="font-medium cursor-pointer flex items-center justify-between">
              Có quảng cáo làm phiền không?
              <span class="text-sm opacity-70 group-open:rotate-180 transition">⌄</span>
            </summary>
            <p class="mt-2 text-sm opacity-80">Chúng tôi hạn chế tối đa các hình thức quảng cáo gây khó chịu để không làm gián đoạn việc đọc của bạn.</p>
          </details>
        </div>
      </div>
    </div>

    <div class="mt-6 p-4 rounded-lg bg-zinc-100/70 dark:bg-zinc-800/50 text-sm">
      Không tìm thấy câu trả lời? <a [routerLink]="['/lien-he']" class="text-blue-600 hover:underline">Liên hệ</a> với chúng tôi.
    </div>
  </div>
</section>
