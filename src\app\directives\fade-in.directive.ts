import { isPlatformBrowser } from '@angular/common';
import { AfterViewInit, Directive, ElementRef, Inject, Input, PLATFORM_ID, Renderer2 } from '@angular/core';

@Directive({
    selector: '[appFadeIn]',
    standalone: true
})
export class FadeInDirective implements AfterViewInit {
  @Input() duration = 500; // Thời gian animation mặc định là 1000ms
  bFadeIn = false;
  constructor(private el: ElementRef, private renderer: Renderer2, @Inject(PLATFORM_ID) private platformId: object) {


  }

  ngAfterViewInit() {
    if (!isPlatformBrowser(this.platformId)) return;
    const observer = new IntersectionObserver(this.handleIntersection.bind(this), {
      threshold: [0, 1]
    });
    observer.observe(this.el.nativeElement);
    this.renderer.addClass(this.el.nativeElement, 'transition-opacity');
    this.renderer.addClass(this.el.nativeElement, 'duration-[' + this.duration + 'ms]');
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      if (entry.isIntersecting && !this.bFadeIn) {
        this.fadeIn();
      }
      if (!entry.isIntersecting && this.bFadeIn) {
        this.fadeOut();
      }
    });
  }

  public fadeIn() {
    this.bFadeIn = true;
    this.renderer.addClass(this.el.nativeElement, 'opacity-100');
    this.renderer.removeClass(this.el.nativeElement, 'opacity-0');
  }

  public fadeOut() {
    this.bFadeIn = false;
    this.renderer.addClass(this.el.nativeElement, 'opacity-0');
    this.renderer.removeClass(this.el.nativeElement, 'opacity-100');
  }
}
