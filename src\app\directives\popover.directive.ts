import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';

@Directive({
    selector: '[appPopover]',
    standalone: true
})
export class PopoverDirective {
  @Input('appPopover') contentTemplate?: TemplateRef<any>;

  private popover!: HTMLElement | undefined;

  constructor(
    private el: ElementRef,
    private viewContainerRef: ViewContainerRef,
  ) { }

  @HostListener('mouseenter') onMouseEnter() {
    this.showPopover();
  }

  @HostListener('mouseleave') onMouseLeave() {
    this.hidePopover();
  }

  private showPopover() {
    if (this.contentTemplate) {
      const view = this.viewContainerRef.createEmbeddedView(
        this.contentTemplate,
      );
      this.popover = view.rootNodes[0];
      this.popover?.classList.add(
        'absolute',
        'z-10',
        'mt-2',
        'w-48',
        'rounded-lg',
        'border',
        'border-neutral-600',
        'shadow-lg',
        'bg-white',
        'p-2',
        'dark:bg-neutral-800',
      );
      this.el.nativeElement.appendChild(this.popover);
    }
  }

  private hidePopover() {
    if (this.popover) {
      this.el.nativeElement.removeChild(this.popover);
      this.popover = undefined;
    }
  }
}
