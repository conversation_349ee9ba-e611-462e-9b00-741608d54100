import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  computed,
  effect,
  inject,
  signal
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RouterModule } from '@angular/router';
import { Genre } from '@schema';
import { GenreService } from '@services/genre.service';

/**
 * Genre Categories Component
 *
 * Displays categorized genre lists with search functionality and enhanced UI.
 * Features:
 * - Categorized genre display (Countries vs Common genres)
 * - Real-time search filtering
 * - Interactive genre chips with states (normal, active, disabled)
 * - Hover information display
 * - Responsive design with modern UI
 */
@Component({
  selector: 'div[app-genre-catagories]',
  imports: [CommonModule, RouterModule],
  templateUrl: './genre-catagories.component.html',
  styleUrl: './genre-catagories.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GenreCatagoriesComponent implements OnInit {
  genreService = inject(GenreService);
  private readonly destroyRef = inject(DestroyRef);

  @Input() routerLinkGenres = true;
  @Input() statusGenres: Record<string, number> = {};

  // Output events
  @Output() clickGenres = new EventEmitter<Genre>();

  // Reactive state with signals
  private readonly genresSignal = signal<Genre[]>([]);
  private readonly searchTermSignal = signal('');

  // Computed properties
  readonly categorizedGenres = computed(() => {
    const genres = this.genresSignal();
    return this.categorizeGenres(genres);
  });

  readonly filteredCatagoriesGenre = computed(() => {
    const searchTerm = this.searchTermSignal();
    const categories = this.categorizedGenres();

    if (!searchTerm.trim()) {
      return categories;
    }

    return this.filterGenresBySearch(categories, searchTerm);
  });

  // Public properties for template
  searchTerm = '';
  genreHovered: Genre | null = null;
  catagoriesGenre = new Map<string, Genre[]>();

  constructor() {
    // Update legacy properties when signals change
    effect(() => {
      this.catagoriesGenre = this.categorizedGenres();
    });
  }

  ngOnInit(): void {
    this.genreService.getGenres().pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((genres) => {
        this.genresSignal.set(genres);
        this.updateCategories();
      });
  }

  /**
   * Categorize genres into countries and common genres
   */
  private categorizeGenres(genres: Genre[]): Map<string, Genre[]> {
    const [genreCountry, genreCommon] = this.partition(genres, (genre: Genre) =>
      ['Manga', 'Manhua', 'Manhwa'].includes(genre.title)
    );

    const categories = new Map<string, Genre[]>();
    categories.set('countries', genreCountry);
    categories.set('genreCommon', genreCommon);

    return categories;
  }

  /**
   * Filter genres by search term
   */
  private filterGenresBySearch(categories: Map<string, Genre[]>, searchTerm: string): Map<string, Genre[]> {
    const filtered = new Map<string, Genre[]>();
    const lowerSearchTerm = searchTerm.toLowerCase();

    for (const [category, genres] of categories) {
      const filteredGenres = genres.filter((genre) =>
        genre.title.toLowerCase().includes(lowerSearchTerm)
      );
      if (filteredGenres.length > 0) {
        filtered.set(category, filteredGenres);
      }
    }

    return filtered;
  }

  /**
   * Update categories (for backward compatibility)
   */
  private updateCategories(): void {
    this.catagoriesGenre = this.categorizedGenres();
  }

  /**
   * Partition array into two arrays based on predicate
   */
  private partition<T>(array: T[], predicate: (item: T) => boolean): [T[], T[]] {
    const truthy: T[] = [];
    const falsy: T[] = [];

    array.forEach((item) => {
      if (predicate(item)) {
        truthy.push(item);
      } else {
        falsy.push(item);
      }
    });

    return [truthy, falsy];
  }

  /**
   * Handle genre click
   */
  clickGenre(genre: Genre): void {
    this.clickGenres.emit(genre);
  }

  /**
   * Handle search input
   */
  filterGenres(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.searchTerm = input.value;
    this.searchTermSignal.set(this.searchTerm);
  }

  /**
   * Clear search
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.searchTermSignal.set('');
  }

  /**
   * Get genre CSS class based on status
   */
  getGenreClass(genreId: number | string): string {
    const status = this.statusGenres[genreId];

    if (status === 1) return 'genre-active';
    if (status === 2) return 'genre-disabled';
    return 'genre-normal';
  }

  /**
   * Check if there are filtered results
   */
  hasFilteredResults(): boolean {
    const filtered = this.filteredCatagoriesGenre();
    return Array.from(filtered.values()).some(genres => genres.length > 0);
  }


}
