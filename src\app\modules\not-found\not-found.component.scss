// ===== 404 NOT FOUND PAGE - MODERN COMIC-THEMED DESIGN =====
// Beautiful, responsive design with animations and comic aesthetics

// ===== MAIN CONTAINER =====
.not-found-container {
  @apply relative w-full overflow-hidden;
  @apply bg-white dark:bg-dark-bg;
  @apply flex items-center justify-center;
}


// ===== MAIN CONTENT =====
.main-content {
  @apply relative z-10 w-full max-w-6xl mx-auto px-6 py-12;
  @apply text-center space-y-6;
}

// ===== HERO SECTION =====
.hero-section {
  @apply space-y-8;
}

// ===== ERROR NUMBER =====
.error-number-container {
  @apply relative flex justify-center items-center;
}

.error-number {
  @apply flex items-center justify-center gap-4;
  @apply text-7xl font-black;
  @apply text-primary-100 dark:text-primary-50;
  @apply relative z-10;
}

.digit {
  @apply inline-block;
  animation: bounce 2s ease-in-out infinite;

  &.digit-4-1 {
    animation-delay: 0s;
  }

  &.digit-0 {
    animation-delay: 0.2s;
  }

  &.digit-4-2 {
    animation-delay: 0.4s;
  }
}

.error-glow {
  @apply absolute inset-0 z-0;
  @apply bg-gradient-to-r from-primary-100/20 via-primary-50/30 to-primary-100/20;
  @apply rounded-full blur-3xl;
  animation: pulse 3s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// ===== CHARACTER ILLUSTRATION =====
.character-illustration {
  @apply flex justify-center items-center;
}

.character-container {
  @apply relative;
}

.character-svg {
  @apply w-40 h-40;
  animation: characterFloat 4s ease-in-out infinite;
}

@keyframes characterFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.05);
  }
}

// ===== ERROR MESSAGE =====
.error-message {
  @apply space-y-6 max-w-2xl mx-auto;
}

.error-title {
  @apply space-y-2;
}

.title-main {
  @apply block text-3xl md:text-4xl font-bold;
  @apply text-primary-100 dark:text-primary-50;
  @apply leading-tight;
}



.error-description {
  @apply text-base md:text-lg leading-relaxed;
  @apply text-neutral-600 dark:text-neutral-300;
  @apply max-w-xl mx-auto;
}

// ===== ACTION SECTION =====
.action-section {
  @apply space-y-6;
}

.primary-actions {
  @apply flex flex-col sm:flex-row gap-4 justify-center items-center;
}

// ===== BUTTONS =====
.btn {
  @apply inline-flex items-center gap-3 px-6 py-3;
  @apply text-base font-semibold rounded-xl;
  @apply transition-all duration-300 ease-in-out;
  @apply transform hover:scale-105 active:scale-95;
  @apply shadow-lg hover:shadow-xl;
  @apply min-w-[160px] justify-center;
}

.btn-primary {
  @apply bg-primary-100 hover:bg-primary-200;
  @apply text-white;
  @apply border-2 border-primary-100 hover:border-primary-200;

  &:hover {
    box-shadow: 0 10px 25px rgba(248, 110, 76, 0.3);
  }
}

.btn-secondary {
  @apply bg-white dark:bg-neutral-800;
  @apply text-neutral-700 dark:text-neutral-200;
  @apply border-2 border-neutral-300 dark:border-neutral-600;
  @apply hover:bg-neutral-50 dark:hover:bg-neutral-700;
  @apply hover:border-neutral-400 dark:hover:border-neutral-500;

  &:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
}

.btn-icon {
  @apply w-5 h-5 flex-shrink-0;
}


// ===== RESPONSIVE DESIGN =====
@media (max-width: 640px) {
  .error-number {
    @apply text-6xl gap-2;
  }

  .character-svg {
    @apply w-32 h-32;
  }

  .main-content {
    @apply px-4 py-8 space-y-4;
  }

  .quick-nav-grid {
    @apply grid-cols-1 gap-4;
  }

  .nav-card {
    @apply p-4;
  }

  .floating-shapes .shape {
    @apply hidden;
  }
}

@media (max-width: 480px) {
  .error-number {
    @apply text-5xl;
  }

  .title-main {
    @apply text-2xl;
  }

  .btn {
    @apply px-4 py-2 text-sm min-w-[120px];
  }

  .primary-actions {
    @apply flex-col gap-3;
  }
}

// ===== ACCESSIBILITY =====
@media (prefers-reduced-motion: reduce) {
  .digit,
  .error-glow,
  .character-svg,
  .shape {
    animation: none;
  }

  .btn,
  .nav-card {
    @apply transform-none;

    &:hover {
      @apply transform-none;
    }
  }
}

// ===== HIGH CONTRAST MODE =====
@media (prefers-contrast: high) {
  .nav-card {
    @apply border-2 border-black dark:border-white;
  }

  .btn-primary {
    @apply border-2 border-primary-200;
  }

  .btn-secondary {
    @apply border-2 border-neutral-500;
  }
}

// ===== PRINT STYLES =====
@media print {
  .not-found-container {
    @apply bg-white text-black;
  }

  .floating-shapes,
  .bg-gradient,
  .error-glow {
    @apply hidden;
  }

  .character-svg {
    @apply grayscale;
  }

  .btn {
    @apply border border-black bg-white text-black;
  }
}