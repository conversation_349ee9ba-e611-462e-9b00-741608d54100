import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'genreFilter', pure: true, standalone: true })
export class GenreFilterPipe implements PipeTransform {
  transform<T extends { title?: string; name?: string }>(list: T[] | null | undefined, q: string | null | undefined): T[] {
    if (!Array.isArray(list)) return [];
    const term = (q || '').trim().toLowerCase();
    if (!term) return list;
    return list.filter((g) => {
      const text = (g.title || (g as any)['name'] || '').toLowerCase();
      return text.includes(term);
    });
  }
}
