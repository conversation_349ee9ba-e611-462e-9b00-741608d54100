import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'numeral',
    standalone: true
})
export class NumeralPipe implements PipeTransform {
  transform(value: any, format = '0,0a'): string {
    return this.formatToShortenedNumber(value);
  }
  formatToShortenedNumber(value: number): string {
    if (value >= 1e9) {
      return `${(value / 1e9).toFixed(1).replace(/\.0$/, '')}B`; // 1B
    }
    if (value >= 1e6) {
      return `${(value / 1e6).toFixed(1).replace(/\.0$/, '')}M`; // 1M
    }
    if (value >= 1e3) {
      return `${(value / 1e3).toFixed(1).replace(/\.0$/, '')}K`; // 1K
    }

    return value.toString(); // Return the original value if it's less than 1000

  }

}
