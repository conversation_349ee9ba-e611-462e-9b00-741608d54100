import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterLink, RouterModule } from '@angular/router';
import { ClauseComponent } from './clause.component';



@NgModule({
  declarations: [ClauseComponent],
  imports: [
    CommonModule,
    RouterModule,
    RouterLink,
    RouterModule.forChild([
      {
        path: '', component: ClauseComponent,
      }
    ])
    ,
  ]
})
export class ClauseModule { }
