import { CommonModule, isPlatformBrowser } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChild, ElementRef, EventEmitter, Inject, Input, OnChanges, OnInit, Output, PLATFORM_ID, SimpleChanges, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { OptimizedBaseComponent } from '../base/optimized-base.component';
import { fromEvent, Subscription, throttleTime } from 'rxjs';


@Component({
  selector: 'div[app-loop-scroll]',
  standalone: true,
  templateUrl: './loop-scroll.component.html',
  styleUrl: './loop-scroll.component.scss',
  imports: [CommonModule],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoopScrollComponent extends OptimizedBaseComponent implements OnInit, AfterViewInit, OnChanges {

  @Input() selectedID?: any;
  @ContentChild("ItemTemplate")
  itemTemplateRef!: TemplateRef<any>;
  @ViewChild('VirtualScroll')
  virtualScrollRef: ElementRef<HTMLElement> | null = null;
  @Input()
  preloadItemCount = 24;
  @Input()
  gridSize = 1;
  @Input() itemHeight = 32;
  preScrollIdx = 0;
  @Output()
  onChange = new EventEmitter<number>();
  @ViewChild('VirtualScrollSpace')
  virtualScrollSpaceRef!: ElementRef<HTMLElement>;
  @ViewChild('VirtualScrollContent')
  virtualScrollContentRef!: ElementRef<HTMLElement>;
  @Input() allitems: any[] = [];
  preLoadItems: any[] = [];

  constructor(
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object,
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.preLoadItems = this.allitems.slice(0, this.preloadItemCount);
  }
  ngAfterViewInit(): void {
    if (this.allitems.length == 0) return;
    this.setUpScroll();
  }
  ngOnChanges(changes: SimpleChanges): void {    
    if (changes['allitems'] && !changes['allitems'].firstChange) {
      this.setUpScroll();
    }
    if(changes["gridSize"] && !changes["gridSize"].firstChange) {
      this.setUpScroll();
    }
  }
  subscription ?: Subscription;
  setUpScroll() {
    if (!isPlatformBrowser(this.platformId)) return;
    this.preLoadItems = this.allitems.slice(0, this.preloadItemCount);
    this.virtualScrollSpaceRef.nativeElement.style.height = `${this.nRow * this.realItemHeight}px`;
    this.virtualScrollContentRef.nativeElement.style.transform = `translateY(0px)`;

    if (this.virtualScrollRef) {
      this.preScrollIdx = 0;
      this.subscription?.unsubscribe();
      this.subscription = fromEvent(this.virtualScrollRef.nativeElement, 'scroll').pipe(throttleTime(16)).subscribe(this.onVirtualScroll);
      if (this.selectedID) {
        const idx = this.allitems.findIndex((item) => item.id == this.selectedID);
        this.virtualScrollRef?.nativeElement.scrollTo(0, Math.max(Math.round(idx/this.gridSize), 0) * this.realItemHeight);
      }
      else {
        this.virtualScrollRef?.nativeElement?.scrollTo(0, 0);
      }
    }
    
    this.safeMarkForCheck();
  }
  get nRow(): number {
    return Math.floor((this.allitems.length - 1) / this.gridSize) + 1;
  }
  get realItemHeight(): number {
    return this.itemHeight;
  }

  goToItem(idx: number) {
    this.virtualScrollRef?.nativeElement.scrollTo(0, Math.max(Math.round(idx / this.gridSize), 0) * this.realItemHeight);
  }

  onVirtualScroll = (e: Event) => {

    const ele = (e.target as HTMLElement);
    if (ele.scrollTop + ele.clientHeight > this.nRow * this.realItemHeight) {
      return;
    }
    const idx = Math.floor(ele.scrollTop / this.realItemHeight);
    this.onChange.emit(idx);

    const oneThirdLength = Math.round(this.preloadItemCount / 3);
    const leftIdx = Math.max(idx - oneThirdLength, 0);

    // Use requestAnimationFrame for smoother scrolling
    if (Math.abs(idx - this.preScrollIdx) > oneThirdLength / this.gridSize) {
      requestAnimationFrame(() => {
        const twoThirdLength = Math.round(this.preloadItemCount * 2 / 3);
        const rightIdx = twoThirdLength + idx - Math.min(idx - oneThirdLength, 0);

        this.preLoadItems = this.allitems.slice(leftIdx * this.gridSize, rightIdx * this.gridSize);
        this.virtualScrollContentRef.nativeElement.style.transform = `translate3d(0, ${leftIdx * this.realItemHeight}px, 0)`;
        this.preScrollIdx = idx;
        this.safeMarkForCheck();        
      });
    }
  }

}
