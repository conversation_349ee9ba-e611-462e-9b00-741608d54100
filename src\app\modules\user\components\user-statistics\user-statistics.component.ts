import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'main[app-user-statistics]',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="user-statistics-container">
      <div class="page-header">
        <h2 class="page-title">
          <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="20" x2="18" y2="10"/>
            <line x1="12" y1="20" x2="12" y2="4"/>
            <line x1="6" y1="20" x2="6" y2="14"/>
          </svg>
          Thống kê
        </h2>
        <p class="page-description">Xem thống kê hoạt động đọc truyện của bạn</p>
      </div>

      <div class="content-card">
        <div class="empty-state">
          <div class="empty-illustration">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <line x1="18" y1="20" x2="18" y2="10"/>
              <line x1="12" y1="20" x2="12" y2="4"/>
              <line x1="6" y1="20" x2="6" y2="14"/>
            </svg>
          </div>
          <h3 class="empty-title">Đang phát triển</h3>
          <p class="empty-description">
            Tính năng thống kê đang được phát triển. Sẽ sớm có biểu đồ và số liệu chi tiết!
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .user-statistics-container {
      @apply space-y-6;
    }

    .page-header {
      @apply space-y-2;
    }

    .page-title {
      @apply flex items-center gap-3 text-2xl font-bold text-neutral-900 dark:text-light-text;
    }

    .title-icon {
      @apply w-8 h-8 text-lime-500;
      fill: none;
      stroke: currentColor;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .page-description {
      @apply text-neutral-600 dark:text-neutral-400;
    }

    .content-card {
      @apply bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-sm p-12;
      backdrop-filter: blur(20px);
      background: rgba(255, 255, 255, 0.95);
      
      .dark & {
        background: rgba(31, 41, 55, 0.95);
      }
    }

    .empty-state {
      @apply text-center space-y-6;
    }

    .empty-illustration {
      @apply flex justify-center;
    }

    .empty-icon {
      @apply w-24 h-24 text-neutral-300 dark:text-neutral-600;
      fill: none;
      stroke: currentColor;
      stroke-width: 1;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .empty-title {
      @apply text-xl font-semibold text-neutral-900 dark:text-light-text;
    }

    .empty-description {
      @apply text-neutral-600 dark:text-neutral-400 max-w-md mx-auto leading-relaxed;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserStatisticsComponent {
}
