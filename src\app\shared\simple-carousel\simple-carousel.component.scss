
.sc-stage {
  display: flex;
  user-select: none;
  will-change: transform;
  transition: none; /* Default no transition, controlled by <PERSON><PERSON> */
  gap: var(--sc-gap);
  width: 100%;
}
.sc-stage.transitioning {
  transition: transform 0.4s cubic-bezier(0.25, 1, 0.5, 1);
}


.sc-item { 
  flex: 0 0 calc((100% - (var(--sc-items) - 1) * var(--sc-gap)) / var(--sc-items));
}
.sc-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
  border: none;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  border-radius: 9999px;
}
.sc-nav.prev {
  left: 6px;
}
.sc-nav.next {
  right: 6px;
}

.sc-wrapper:focus-visible { outline: 2px solid #2563eb; outline-offset: 2px; }



.mobile-nav-left {
  @apply absolute top-1/2 left-2 z-10 w-12 h-12 cursor-pointer;
  @apply -translate-y-1/2 rounded-xl overflow-hidden;
  @apply bg-black/20 hover:bg-black/60 hover:scale-110 active:scale-95;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100/50;

  .mobile-nav-icon {
    @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
    @apply w-10 h-10 text-white opacity-40;
    @apply transition-opacity duration-200;
  }

  &:hover .mobile-nav-icon {
    @apply opacity-80;
  }
}

.mobile-nav-right {
  @apply absolute top-1/2 right-2 z-10 w-12 h-12 cursor-pointer;
  @apply -translate-y-1/2 rounded-xl overflow-hidden;
  @apply bg-black/20 hover:bg-black/60 hover:scale-110 active:scale-95;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100/50;

  .mobile-nav-icon {
    @apply absolute top-1/2 right-1/2 translate-x-1/2 -translate-y-1/2;
    @apply w-10 h-10 text-white opacity-40;
    @apply transition-opacity duration-200;
  }

  &:hover .mobile-nav-icon {
    @apply opacity-80;
  }
}

