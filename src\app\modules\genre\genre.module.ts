import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { Selection2Component } from '@components/common/selection-2/selection-2.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { ComicDescriptionPipe } from '@pines/description.pipe';
import { NumeralPipe } from '@pines/numeral.pipe';
import { GenreComponent } from './genre.component';

@NgModule({
  declarations: [GenreComponent],
  imports: [
    RouterModule.forChild([{ path: '', component: GenreComponent }]),
    CommonModule,
    PaginationComponent,
    BreadcrumbComponent,
    GridComicComponent,
    NumeralPipe,
    ComicDescriptionPipe,
    ClickOutsideDirective,
    Selection2Component
  ]
})
export class GenreModule { }
