// Enhanced User Header Component Styles
.user-header-container {
  @apply space-y-6;
}


// Main Content
.header-content {
  @apply space-y-6;
}

// Enhanced Card Base Styles
.profile-card,
.info-card,
.password-card {
  @apply bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-sm hover:shadow-lg transition-all duration-200;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);

  .dark & {
    background: rgba(38, 38, 38, 0.95);
  }
}

// Card Headers
.profile-card-header,
.info-card-header,
.password-card-header {
  @apply flex items-center justify-between p-6 border-b border-neutral-200 dark:border-neutral-700;
}

.card-title {
  @apply flex items-center gap-3 text-xl font-bold text-neutral-900 dark:text-light-text;
}

.title-icon {
  @apply w-6 h-6 text-primary-100;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.edit-btn {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium text-neutral-600 dark:text-neutral-400 hover:text-primary-100 hover:bg-primary-100/10 dark:hover:bg-primary-100/20 rounded-lg transition-all duration-200 border-none cursor-pointer;

  &.active {
    @apply text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20;
  }

  &:focus {
    @apply outline-none ring-2 ring-primary-100/50;
  }
}

.btn-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Profile Card Content
.profile-card-content {
  @apply p-6 space-y-6;
}

.avatar-section {
  @apply flex flex-col items-center gap-4;
}

.avatar-container {
  @apply relative;
}

.avatar-upload-label {
  @apply relative block cursor-pointer;
}

.user-avatar {
  @apply w-32 h-32 rounded-full object-cover border-4 border-neutral-200 dark:border-neutral-600 transition-all duration-300;

  .avatar-upload-label:hover & {
    filter: brightness(0.8);
  }
}

.avatar-overlay {
  @apply absolute inset-0 bg-black bg-opacity-0 flex flex-col items-center justify-center rounded-full transition-all duration-300 opacity-0;
  
  .avatar-upload-label:hover & {
    background-opacity: 0.6;
    opacity: 1;
  }
}

.camera-icon {
  @apply w-8 h-8 text-white mb-1;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.upload-text {
  @apply text-white text-sm font-medium;
}

.level-badge {
  @apply flex items-center gap-2 px-4 py-2 bg-primary-100/10 dark:bg-primary-100/20 text-primary-100 rounded-full;
}

.level-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.level-text {
  @apply font-bold;
}

// User Stats
.user-stats {
  @apply grid grid-cols-3 gap-4;
}

.stat-item {
  @apply text-center p-4 bg-neutral-50 dark:bg-neutral-700/50 rounded-xl;
}

.stat-label {
  @apply text-xs text-neutral-500 dark:text-neutral-400 font-medium mb-1;
}

.stat-value {
  @apply text-lg font-bold text-neutral-900 dark:text-light-text;
}

// Level Type Section
.level-type-section {
  @apply space-y-3;
}

.section-subtitle {
  @apply text-sm font-semibold text-neutral-700 dark:text-neutral-300;
}

.level-type-options {
  @apply flex gap-3;
}

.level-type-option {
  @apply flex-1 cursor-pointer;
}

.level-radio {
  @apply sr-only;

  &:checked + .level-option-content {
    @apply bg-primary-100/10 dark:bg-primary-100/20 border-primary-100 text-primary-100;
  }
}

.level-option-content {
  @apply flex flex-col items-center p-3 border-2 border-neutral-200 dark:border-neutral-600 rounded-lg transition-all duration-200 hover:border-primary-100/50;
}

.level-option-title {
  @apply text-sm font-medium;
}

.level-option-desc {
  @apply text-xs text-neutral-500 dark:text-neutral-400;
}

// Motto Section
.motto-section {
  @apply space-y-3;
}

.motto-input-group {
  @apply space-y-3;
}

.motto-textarea {
  @apply w-full p-3 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-700 text-neutral-900 dark:text-light-text placeholder-neutral-500 dark:placeholder-neutral-400 resize-none focus:ring-2 focus:ring-primary-100 focus:border-primary-100 transition-colors duration-200;
}

.motto-save-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-primary-100 hover:bg-primary-200 disabled:bg-neutral-400 text-white font-medium rounded-lg transition-colors duration-200 border-none cursor-pointer;

  &:disabled {
    @apply cursor-not-allowed;
  }

  &:focus {
    @apply outline-none ring-2 ring-primary-100/50;
  }
}

// Join Date Section
.join-date-section {
  @apply pt-4 border-t border-neutral-200 dark:border-neutral-700;
}

.join-date-item {
  @apply flex items-center gap-3;
}

.calendar-icon {
  @apply w-5 h-5 text-neutral-500 dark:text-neutral-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.join-date-content {
  @apply flex flex-col;
}

.join-date-label {
  @apply text-sm text-neutral-500 dark:text-neutral-400;
}

.join-date-value {
  @apply text-sm font-medium text-neutral-900 dark:text-light-text;
}

// Form Styles
.info-card-content,
.password-card-content {
  @apply p-6;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply flex items-center gap-2 text-sm font-medium text-neutral-700 dark:text-neutral-300;
}

.label-icon {
  @apply w-4 h-4 text-neutral-500 dark:text-neutral-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.form-input {
  @apply w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-700 text-neutral-900 dark:text-light-text placeholder-neutral-500 dark:placeholder-neutral-400 focus:ring-2 focus:ring-primary-100 focus:border-primary-100 transition-colors duration-200;

  &.error {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
  }
}

.password-input-group {
  @apply relative;
}

.password-toggle {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

.form-actions {
  @apply flex items-center gap-3 pt-4;
}

.cancel-btn {
  @apply px-4 py-2 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 font-medium border border-neutral-300 dark:border-neutral-600 hover:border-neutral-400 dark:hover:border-neutral-500 rounded-lg transition-colors duration-200 cursor-pointer;

  &:focus {
    @apply outline-none ring-2 ring-neutral-500/50;
  }
}

.save-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-primary-100 hover:bg-primary-200 disabled:bg-neutral-400 text-white font-medium rounded-lg transition-colors duration-200 border-none cursor-pointer;

  &:disabled {
    @apply cursor-not-allowed;
  }

  &:focus {
    @apply outline-none ring-2 ring-primary-100/50;
  }
}

// Info Display (View Mode)
.info-display {
  @apply space-y-4;
}

.info-item {
  @apply flex justify-between items-center py-3 border-b border-neutral-100 dark:border-neutral-700 last:border-b-0;
}

.info-label {
  @apply text-sm font-medium text-neutral-500 dark:text-neutral-400;
}

.info-value {
  @apply text-sm font-medium text-neutral-900 dark:text-light-text;
}

// Password Info
.password-info {
  @apply space-y-4;
}

.password-status {
  @apply flex items-center gap-3 p-4 bg-lime-50 dark:bg-lime-900/20 rounded-lg;
}

.status-icon {
  @apply w-5 h-5 text-lime-600 dark:text-lime-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.status-text {
  @apply text-sm font-medium text-lime-700 dark:text-lime-300;
}

.password-description {
  @apply text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed;
}

// Responsive Design
@media (max-width: 768px) {
  .user-stats {
    @apply grid-cols-1 gap-3;
  }

  .level-type-options {
    @apply flex-col;
  }

  .form-grid {
    @apply grid-cols-1;
  }

  .form-actions {
    @apply flex-col-reverse;
  }

  .cancel-btn,
  .save-btn {
    @apply w-full justify-center;
  }
}

// Accessibility
.form-input:focus,
.motto-textarea:focus {
  @apply outline-none;
}

// High Contrast Mode
@media (prefers-contrast: high) {
  .profile-card,
  .info-card,
  .password-card {
    @apply border-2 border-black dark:border-white;
  }

  .form-input {
    @apply border-2 border-neutral-600;

    &.error {
      @apply border-2 border-red-600;
    }
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  .profile-card,
  .info-card,
  .password-card,
  .edit-btn,
  .form-input,
  .save-btn,
  .cancel-btn {
    @apply transition-none;
  }

  .avatar-overlay,
  .user-avatar {
    @apply transition-none;
  }

  .loading-card {
    animation: none;
  }
}

// Performance Optimizations
.profile-card,
.info-card,
.password-card,
.avatar-overlay {
  transform: translateZ(0);
  backface-visibility: hidden;
}
