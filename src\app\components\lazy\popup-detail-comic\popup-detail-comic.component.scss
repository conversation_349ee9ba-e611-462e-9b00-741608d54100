// ===== COMPACT STATS SECTION =====
.stats-section-compact {
  @apply flex justify-around p-1 bg-neutral-100 dark:bg-neutral-800/50 border-t border-neutral-200 dark:border-neutral-700;
}

.stat-compact {
  @apply flex items-center gap-1;
}

.stat-icon {
  @apply w-4 h-4;
}

.stat-value {
  @apply text-sm font-semibold;
}

// ===== MODERN COMIC DETAIL POPUP STYLES =====
// Beautiful, responsive design with comic website aesthetics

// ===== MAIN CONTAINER =====
.popup-detail-container {
  @apply flex flex-col w-[28rem] max-w-[90vw] bg-white dark:bg-neutral-900 rounded-2xl shadow-lg overflow-hidden;

  // Modern backdrop blur effect
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);

  .dark & {
    background: rgba(23, 23, 23, 0.95);
  }

  // Smooth entrance animation
  animation: slideInUp 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

  // Decrease line height for better spacing
  line-height: 1.4;

}

// ===== BACKGROUND GRADIENT =====
.popup-background {
  @apply absolute inset-0 opacity-30 pointer-events-none;
}

// ===== MAIN CONTENT =====
.popup-content {
  @apply relative z-10 p-4 flex flex-col gap-3;
}

// ===== HEADER SECTION =====
.popup-header {
  @apply flex flex-col p-2 bg-neutral-50 dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700;

  // Decrease padding for compact view
  padding: 4px;
}

.title-section {
  @apply flex items-center justify-between;
}

.comic-title {
  @apply text-xl font-bold text-neutral-900 dark:text-light-text leading-tight;
  flex: 1;
}

// ===== STATUS BADGE =====
.status-badge {
  @apply flex items-center gap-2;
}

.status-indicator {
  @apply flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium;

  &.ongoing {
    @apply text-blue-700  dark:text-blue-300;
  }

  &.completed {
    @apply text-green-700  dark:text-green-300;
  }
}

.status-dot {
  @apply w-2 h-2 rounded-full bg-current animate-pulse;
}

.status-icon {
  @apply w-4 h-4 animate-pulse;
}

.status-text {
  @apply font-semibold;
}

// ===== AUTHOR SECTION =====
.author-section {
  @apply flex items-center gap-2 text-neutral-600 dark:text-neutral-400;
}

.author-icon {
  @apply w-4 h-4 stroke-current;
}

.author-name {
  @apply text-sm font-medium;
}

// ===== GENRES SECTION =====
.genres-section {
  @apply flex flex-wrap gap-2 p-1;
}

.genres-container {
  @apply flex flex-wrap gap-2;
}

.genre-tag {
  @apply px-1 py-0.5 text-xs font-semibold rounded-full;
  @apply bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300;

  &.primary-genre {
    @apply bg-gradient-to-r from-primary-100 to-primary-200 text-white;
    box-shadow: 0 4px 15px rgba(248, 110, 76, 0.3);
  }

  // Decrease padding and font size for compact view
  padding: 1px 3px;
  font-size: 0.7rem;
}

// ===== STATS SECTION =====
.stats-section {
  @apply grid grid-cols-3 gap-4;
}

.stat-item {
  @apply flex flex-col items-center gap-2 p-3 rounded-xl;
  @apply bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700;

  &.rating {
    .stat-icon {
      @apply text-yellow-500;
    }
  }

  &.views {
    .stat-icon {
      @apply text-blue-500;
    }
  }

  &.chapters {
    .stat-icon {
      @apply text-green-500;
    }
  }
}

.stat-icon {
  @apply w-5 h-5 transition-transform duration-200;

  .stat-item:hover & {
    @apply scale-110;
  }
}

.stat-value {
  @apply text-lg font-bold text-neutral-900 dark:text-light-text;

  // Decrease font size for compact view
  font-size: 0.875rem;
}

.stat-label {
  @apply text-xs text-neutral-600 dark:text-neutral-400 font-medium;
}

// ===== DESCRIPTION SECTION =====
.description-section {
  @apply p-4 text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed;
}

.description-header {
  @apply flex items-center gap-2 text-neutral-700 dark:text-neutral-300;
}

.description-icon {
  @apply w-4 h-4 stroke-current;
}

.description-title {
  @apply text-sm font-semibold;
}

.description-text {
  @apply text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed line-clamp-4;

  // Style for HTML content
  :deep(p) {
    @apply mb-2 last:mb-0;
  }

  :deep(strong) {
    @apply font-semibold text-neutral-800 dark:text-neutral-200;
  }

  :deep(em) {
    @apply italic;
  }
}

// ===== ANIMATIONS =====
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 640px) {
  .popup-detail-container {
    @apply w-[95vw] mx-2;
  }

  .popup-content {
    @apply p-4 gap-4;
  }

  .comic-title {
    @apply text-lg;
  }

  .stats-section {
    @apply grid-cols-1 gap-2;
  }

  .stat-item {
    @apply flex-row justify-start p-2;

    .stat-icon {
      @apply w-4 h-4;
    }

    .stat-value {
      @apply text-base;
    }
  }

  .action-section {
    @apply flex-col gap-2;
  }

  .action-btn {
    @apply py-2.5;
  }
}

// ===== ACCESSIBILITY =====
@media (prefers-reduced-motion: reduce) {
  .popup-detail-container,
  .status-dot,
  .status-icon,
  .genre-tag,
  .stat-item,
  .action-btn {
    animation: none;
    transition: none;
  }

  .stat-icon,
  .btn-icon {
    transform: none !important;
  }
}

// ===== HIGH CONTRAST MODE =====
@media (prefers-contrast: high) {
  .popup-detail-container {
    @apply border-2 border-black dark:border-white;
  }

  .genre-tag {
    @apply border border-current;
  }

  .stat-item {
    @apply border-2;
  }

  .action-btn {
    @apply border-2 border-current;
  }
}
