// Modern Breadcrumb Component - Comic Website Design
.breadcrumb-container {
  @apply w-full rounded-lg border border-neutral-100/50 dark:border-neutral-700/50 overflow-hidden;
  @apply bg-neutral-50 dark:bg-neutral-800; // Default background

  // Transparent background style
  &.breadcrumb-transparent {
    @apply bg-transparent dark:bg-transparent border-transparent dark:border-transparent shadow-none;

    .breadcrumb-header {
      @apply bg-transparent dark:bg-transparent border-transparent dark:border-transparent;
    }

    .breadcrumb-icon-wrapper {
      @apply bg-primary-100/80 backdrop-blur-sm;
    }

    .breadcrumb-link {
      @apply text-neutral-100 font-semibold hover:bg-white/20 dark:hover:bg-black/20;
    }

    .breadcrumb-current {
      @apply bg-primary-100/20 dark:bg-primary-100/10 text-white;
    }
    .divider-icon {
      @apply text-white font-bold;
    }
  }

  // Default background style (explicit)
  &.breadcrumb-default {
    @apply bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700;

    .breadcrumb-header {
      @apply bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700;
    }

    .breadcrumb-link {
      @apply text-neutral-600 dark:text-neutral-200 hover:text-primary-100 hover:bg-neutral-100 dark:hover:bg-neutral-700;
    }

    .breadcrumb-current {
      @apply text-primary-100 dark:text-primary-100 bg-primary-100/10 dark:bg-primary-100/20;
    }

    .divider-icon {
      @apply text-neutral-400 dark:text-neutral-200;
    }
  }
}

// Breadcrumb Header
.breadcrumb-header {
  @apply flex items-center gap-3 p-1.5 bg-neutral-50 dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700;
}


// Breadcrumb List
.breadcrumb-list {
  @apply flex items-center gap-1 flex-1 min-w-0;
}

.breadcrumb-item {
  @apply flex items-center md:gap-2;

  &.breadcrumb-item-current {
    @apply text-primary-100;
  }
}

// Breadcrumb Links
.breadcrumb-link {
  @apply inline-flex items-center  px-1 md:px-2 text-sm font-medium text-neutral-600 dark:text-neutral-200 hover:text-primary-100 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-100/50;
}

.breadcrumb-text {
  @apply truncate max-w-36 sm:max-w-48 md:max-w-none;
}

.breadcrumb-current {
  @apply truncate w-auto px-2 py-1 text-sm line-clamp-1 font-bold text-primary-100 bg-primary-100/10 dark:bg-primary-100/20 rounded-md;
}

// Divider
.breadcrumb-divider {
  @apply flex items-center justify-center;
}

.divider-icon {
  @apply w-4 h-4 text-neutral-400 dark:text-neutral-200;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Mobile Breadcrumb
.breadcrumb-mobile {
  @apply flex md:hidden items-center justify-between p-3 bg-white dark:bg-dark-bg;
}

.breadcrumb-back-button {
  @apply flex items-center gap-2 px-3 py-2 text-neutral-600 dark:text-neutral-200 hover:text-primary-100 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-lg border-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-100/50;
}

.back-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.back-text {
  @apply text-sm font-medium truncate max-w-24;
}

.current-page-mobile {
  @apply flex items-center;
}

.current-page-text {
  @apply text-sm font-bold text-primary-100 truncate max-w-48;
}

// Responsive Design
@media (min-width: 768px) {
  .breadcrumb-mobile {
    @apply hidden;
  }
}

@media (max-width: 767px) {
  .breadcrumb-container {
    @apply border-0 shadow-none bg-transparent dark:bg-transparent;
  }
}

