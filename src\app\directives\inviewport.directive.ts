import { isPlatform<PERSON>rowser } from "@angular/common";
import { Directive, ElementRef, EventEmitter, Inject, Input, OnD<PERSON>roy, OnInit, Output, PLATFORM_ID } from "@angular/core";

@Directive({
    selector: '[inViewport]'
})
export class InViewportDirective implements OnInit, OnDestroy {
    @Output() visible = new EventEmitter<void>();
    @Input() root: Element | null = null;
    @Input() rootMargin: string = '0px 0px 50px 0px';
    @Input() threshold: number | number[] = 0.01;
    @Input() emitOnce: boolean = true;
    private observer: IntersectionObserver | null = null;

    constructor(private el: ElementRef, @Inject(PLATFORM_ID) private platformId: object
    ) { }

    ngOnInit() {
        if (!isPlatformBrowser(this.platformId)) return;
        this.initIntersectionObserver();
    }
    initIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(entries => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.visible.emit();
                        if (this.emitOnce) {
                            this.observer?.unobserve(this.el.nativeElement);
                        }
                    }
                });
            }, {
                root: this.root,
                rootMargin: this.rootMargin,
                threshold: this.threshold
            });

            this.observer.observe(this.el.nativeElement);
        }
    }

    reset() {
        this.observer?.unobserve(this.el.nativeElement);
        this.initIntersectionObserver();
    }

    ngOnDestroy(): void {
        this.observer?.disconnect();
    }
}
