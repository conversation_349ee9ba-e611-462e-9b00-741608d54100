import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { RouterLink } from '@angular/router';
import { BannerComponent } from '@components/common/ad/banner/banner.component';
import { Banner2Component } from '@components/common/ad/banner2/banner2.component';
import { Banner3Component } from '@components/common/ad/banner3/banner3.component';

@NgModule({

    exports: [
        BannerComponent,
        Banner2Component,
        Banner3Component,

    ], imports: [CommonModule,
        BannerComponent,
        Banner2Component,
        Banner3Component,
        RouterLink,
        ReactiveFormsModule,
    ], providers: [provideHttpClient(withInterceptorsFromDi())]
})
export class AdsModule { }
