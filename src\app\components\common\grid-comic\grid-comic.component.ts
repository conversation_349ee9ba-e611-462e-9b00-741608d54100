import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  ContentChild,
  HostListener,
  Inject,
  Input,
  NgZone,
  PLATFORM_ID,
  signal,
  TemplateRef,
  TrackByFunction
} from '@angular/core';
import { Comic } from '@schema';
import { SettingService } from '@services/setting.service';
import { StorageService } from '@services/storage.service';
import { Subscription, timer } from 'rxjs';
import { PopupDetailComicComponent } from '../../lazy/popup-detail-comic/popup-detail-comic.component';
import { OptimizedBaseComponent } from '../base/optimized-base.component';
import { ComicCardComponent } from '../comic-card/Ver1/comic-card.component';
import { ComicCardV2Component } from '../comic-card/Ver2/comic-card-v2.component';
@Component({
  selector: 'div[app-grid-comic]',
  templateUrl: './grid-comic.component.html',
  styleUrl: './grid-comic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, ComicCardComponent, ComicCardV2Component, PopupDetailComicComponent],
  host: {
    class: '@container/main'
  }
})
export class GridComicComponent extends OptimizedBaseComponent {

  @Input() nPreview = 40;
  @Input() _class?: string;
  @Input() title!: string;
  @Input() isLoading = false;
  @ContentChild('actionTemplate') actionTemplate?: TemplateRef<any>;
  @ContentChild('iconTemplate') iconTemplate?: TemplateRef<any>;
  @ContentChild('toolTemplate') toolTemplate?: TemplateRef<any>;
  @ContentChild('emptyTemplate') emptyTemplate?: TemplateRef<any>;

  @Input()
  set listComics(value: Comic[]) {
    if (value.length == 0) {
      this.listComicsSignal.set(Array(this.nPreview).fill(undefined));
      return;
    }
    if (value !== this.listComicsSignal()) {
      this.listComicsSignal.set(value || Array(this.nPreview).fill(undefined));
    }
  }
  readonly classSmall = "grid gap-[12px] grid-cols-3 @lg/main:grid-cols-4 @2xl/main:grid-cols-5  @4xl/main:grid-cols-6 @5xl/main:grid-cols-7 @6xl/main:grid-cols-8 mx-2";
  readonly classMedium = "grid gap-[12px] grid-cols-2 @lg/main:grid-cols-3 @2xl/main:grid-cols-4  @4xl/main:grid-cols-5 @5xl/main:grid-cols-6 @6xl/main:grid-cols-7 mx-2";

  // Signals for reactive state management
  private readonly girdTypeSignal = signal(0);
  private readonly listComicsSignal = signal<Comic[]>([]);
  private readonly hoverComicSignal = signal<Comic | undefined>(undefined);
  private readonly selectedComicsSignal = signal(new Set<number>());
  private readonly isSelectAllSignal = signal(false);
  private readonly defaultGridClassSignal = signal(this.classMedium);


  // Computed properties for optimized access
  readonly defaultGridClass = computed(() => this._class ?? this.defaultGridClassSignal());
  readonly girdType = computed(() => this.girdTypeSignal());
  readonly _listComics = computed(() => this.listComicsSignal());
  readonly hoverComic = computed(() => this.hoverComicSignal());
  readonly selectedComics = computed(() => this.selectedComicsSignal());
  readonly isSelectAll = computed(() => this.isSelectAllSignal());
  readonly selectedCount = computed(() => this.selectedComics().size);
  readonly hasSelectedComics = computed(() => this.selectedCount() > 0);

  // TrackBy functions for performance
  readonly trackByComicId: TrackByFunction<Comic> = (index: number, comic: Comic) => {
    return comic?.id ?? index;
  };

  override readonly trackByIndex = (index: number) => index;

  // Performance optimizations

  constructor(
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object,
    private storageService: StorageService,
    private settingService: SettingService,
    private ngZone: NgZone,
  ) {
    super(cd, platformId);
    this.listComics = Array(this.nPreview).fill(undefined);
    this.girdTypeSignal.set(this.storageService.GetGridType());
    // Debounced resize handler

  }

  ngOnInit(): void {
    const v = this.settingService.getSettingValue('cardComicSize') ?? 'medium';
    if (v !== this.defaultGridClassSignal()) {
      this.defaultGridClassSignal.set(v === 'small' ? this.classSmall : this.classMedium);
    }
    this.settingService.settingChanges$.pipe(this.takeUntilDestroy()).subscribe((event: any) => {
      if (event.settingId === 'cardComicSize') {
        this.defaultGridClassSignal.set(event.newValue === 'small' ? this.classSmall : this.classMedium);
      }
    });
  }


  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  subscription?: Subscription;

  onHoverComic(comic?: Comic) {
    this.ngZone.runOutsideAngular(() => {
      this.subscription?.unsubscribe();
      let delay = comic ? 500 : 0;
      this.subscription = timer(delay).subscribe(() => {
        this.hoverComicSignal.set(comic);
      })
    })
  }

  @HostListener('window:scroll', ['$event'])
  onScroll() {
    if (this.hoverComic()) {
      this.hoverComicSignal.set(undefined);
    }
  }

  changeGridType(_: any, type: number) {
    this.girdTypeSignal.set(type);
    this.storageService.SetGridType(type);
  }

  // toggleSelectComic(index: number) {
  //   const currentSelected = new Set(this.selectedComics());
  //   if (currentSelected.has(index)) {
  //     currentSelected.delete(index);
  //   } else {
  //     currentSelected.add(index);
  //   }
  //   this.selectedComicsSignal.set(currentSelected);
  // }

  // selectAllComics(stage: boolean) {
  //   this.isSelectAllSignal.set(stage);
  //   const newSelected = new Set<number>();
  //   if (stage) {
  //     this._listComics().forEach((_, index) => newSelected.add(index));
  //   }
  //   this.selectedComicsSignal.set(newSelected);
  // }

  // deleteSelectedComics() {
  //   const selectedSet = this.selectedComics();
  //   if (!selectedSet.size) {
  //     return;
  //   }
  //   const selectedComicIds = Array.from(selectedSet).map(
  //     (index) => this._listComics()[index].id,
  //   );
  //   this.clickEvent.emit(selectedComicIds);
  // }

}
