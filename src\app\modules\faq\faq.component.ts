import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { SeoService } from '@services/seo.service';

@Component({
  selector: 'main[app-faq]',
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.scss'],
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FaqComponent implements OnInit {
  faqs = [
    { q: 'Làm sao để tìm truyện yêu thích?', a: 'Bạn có thể dùng ô tìm kiếm, lọc theo thể loại hoặc xem mục Truyện Hot.' },
    { q: 'Làm sao để theo dõi truyện?', a: 'Đăng nhập và bấm Theo dõi ở trang chi tiết truyện để nhận thông báo chương mới.' },
    { q: 'Đồng bộ dữ liệu là gì?', a: '<PERSON><PERSON><PERSON> năng giúp bạn chuyển lịch sử đọc và danh sách theo dõi từ website kh<PERSON><PERSON> sang MeTruyenMoi.' },
  ];
  constructor(private seo: SeoService) {}
  ngOnInit(): void {
    this.seo.setFaqSEO();
  }
}
