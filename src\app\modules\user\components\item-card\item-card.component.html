<div 
  [class]="cardClasses"
  (click)="onCardClick()"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
>
  <!-- Selection Checkbox -->
  <div class="item-card__selection" *ngIf="selectable">
    <input 
      type="checkbox" 
      [checked]="selected"
      (click)="$event.stopPropagation()"
      (change)="onCardClick()"
      class="item-card__checkbox"
    >
  </div>

  <!-- Item Icon/Image -->
  <div class="item-card__icon">
    <div class="item-card__icon-container">
      <img 
        [src]="item.template.icon || '/logo.png'"
        [alt]="displayName"
        class="item-card__image"
        loading="lazy"
      >
      
      <!-- Rarity Border -->
      <div class="item-card__rarity-border"></div>
      
      <!-- Equipped Badge -->
      <div class="item-card__equipped-badge" *ngIf="item.isEquipped">
        <i class="icon-equipped"></i>
      </div>
      
      <!-- Quantity Badge -->
      <div class="item-card__quantity" *ngIf="showQuantity && item.quantity && item.quantity > 1">
        {{ displayQuantity }}
      </div>
      
      <!-- Expiry Warning -->
      <div class="item-card__expiry-warning" *ngIf="isExpiringSoon">
        <i class="icon-clock"></i>
      </div>
      
      <!-- Expired Overlay -->
      <div class="item-card__expired-overlay" *ngIf="isExpired">
        <span>Hết hạn</span>
      </div>
    </div>
  </div>

  <!-- Item Info -->
  <div class="item-card__info">
    <!-- Name -->
    <h4 class="item-card__name" [title]="displayName">
      {{ displayName }}
    </h4>
    
    <!-- Rarity -->
    <div class="item-card__rarity">
      <span class="item-card__rarity-text">{{ rarityInfo.displayName }}</span>
    </div>
    
    <!-- Category (for larger sizes) -->
    <div class="item-card__category" *ngIf="size === 'large'">
      <i [class]="'icon-' + categoryInfo.icon"></i>
      <span>{{ categoryInfo.displayName }}</span>
    </div>
    
    <!-- Description (for large size) -->
    <p class="item-card__description" *ngIf="size === 'large'" [title]="displayDescription">
      {{ displayDescription }}
    </p>
    
    <!-- Expiry Date -->
    <div class="item-card__expiry" *ngIf="item.expiresAt && size !== 'small'">
      <i class="icon-clock"></i>
      <span>{{ item.expiresAt | date:'dd/MM/yyyy' }}</span>
    </div>
  </div>

  <!-- Actions -->
  <div class="item-card__actions" *ngIf="showActions && availableActions.length > 0">
    <!-- Quick Action (Primary action for medium/large cards) -->
    <button 
      *ngIf="size !== 'small' && availableActions[0]"
      class="item-card__quick-action"
      [class.item-card__quick-action--primary]="availableActions[0].primary"
      [disabled]="!availableActions[0].enabled"
      (click)="onActionClick(availableActions[0].type, $event)"
      [title]="availableActions[0].label"
    >
      <i [class]="'icon-' + availableActions[0].icon"></i>
      <span *ngIf="size === 'large'">{{ availableActions[0].label }}</span>
    </button>

    <!-- Actions Menu -->
    <div class="item-card__actions-menu" *ngIf="availableActions.length > 1 || size === 'small'">
      <button 
        class="item-card__actions-toggle"
        (click)="onActionsMenuToggle($event)"
        [title]="'Thêm hành động'"
      >
        <i class="icon-more"></i>
      </button>
      
      <div class="item-card__actions-dropdown" *ngIf="showActionsMenu()">
        <button
          *ngFor="let action of availableActions; trackBy: trackByActionType"
          class="item-card__action-item"
          [class.item-card__action-item--primary]="action.primary"
          [disabled]="!action.enabled"
          (click)="onActionClick(action.type, $event)"
        >
          <i [class]="'icon-' + action.icon"></i>
          <span>{{ action.label }}</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="item-card__loading" *ngIf="false">
    <div class="item-card__spinner"></div>
  </div>
</div>
