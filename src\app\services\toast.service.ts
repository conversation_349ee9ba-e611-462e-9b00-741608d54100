// src/app/toast.service.ts
import { ComponentRef, Injectable, ViewContainerRef } from '@angular/core';
import { ToastComponent } from '@components/common/toast/toast.component';
import { BehaviorSubject, timer } from 'rxjs';

export enum ToastType {
  Success = 'success',
  Error = 'error',
  Info = 'info',
  Warning = 'warning',
}

export interface Toast {
  id: number;
  type: ToastType;
  message: string;
  title?: string;
  state: 'enter' | 'leave';
  duration?: number;
  dismissible?: boolean;
  action?: {
    label: string;
    handler: () => void;
  };
}

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private _viewContainerRef?: ViewContainerRef;
  private componentRef?: ComponentRef<ToastComponent>;
  toasts: Toast[] = [];
  toasts$: BehaviorSubject<Toast[]> = new BehaviorSubject<Toast[]>(this.toasts);
  private nextId = 0;

  show(type: ToastType, message: string, options?: {
    title?: string;
    duration?: number;
    dismissible?: boolean;
    action?: { label: string; handler: () => void };
  }): void {
    this.componentRef = this.createDynamicComponent();
    const id = this.nextId++;
    const duration = options?.duration ?? 3000;

    const toast: Toast = {
      id,
      message,
      type,
      state: 'enter',
      title: options?.title,
      duration,
      dismissible: options?.dismissible ?? true,
      action: options?.action
    };

    this.toasts.push(toast);
    this.toasts$.next(this.toasts);

    if (duration > 0) {
      timer(duration).subscribe(() => this.startLeaveAnimation(toast));
    }
  }

  // Convenience methods
  success(message: string, options?: { title?: string; duration?: number; dismissible?: boolean; action?: { label: string; handler: () => void } }): void {
    this.show(ToastType.Success, message, options);
  }

  error(message: string, options?: { title?: string; duration?: number; dismissible?: boolean; action?: { label: string; handler: () => void } }): void {
    this.show(ToastType.Error, message, options);
  }

  info(message: string, options?: { title?: string; duration?: number; dismissible?: boolean; action?: { label: string; handler: () => void } }): void {
    this.show(ToastType.Info, message, options);
  }

  warning(message: string, options?: { title?: string; duration?: number; dismissible?: boolean; action?: { label: string; handler: () => void } }): void {
    this.show(ToastType.Warning, message, options);
  }

  startLeaveAnimation(toast: Toast): void {
    toast.state = 'leave';
    this.toasts$.next(this.toasts);

    timer(300).subscribe(() => this.remove(toast));
  }

  remove(toast: Toast): void {
    this.toasts = this.toasts.filter((t) => t.id !== toast.id);
    this.toasts$.next(this.toasts);
  }

  dismiss(toastId: number): void {
    const toast = this.toasts.find(t => t.id === toastId);
    if (toast) {
      this.startLeaveAnimation(toast);
    }
  }

  clear(): void {
    this.toasts.forEach(toast => this.startLeaveAnimation(toast));
  }
  set viewContainerRef(viewContainerRef: ViewContainerRef | undefined) {
    this._viewContainerRef = viewContainerRef;
  }

  private createDynamicComponent(): ComponentRef<ToastComponent> {
    if (this.componentRef) {
      if (!this.componentRef.hostView.destroyed) {
        return this.componentRef;
      }
    }
    const componentRef = this._viewContainerRef?.createComponent<ToastComponent>(ToastComponent)!;

    return componentRef;
  }
}
