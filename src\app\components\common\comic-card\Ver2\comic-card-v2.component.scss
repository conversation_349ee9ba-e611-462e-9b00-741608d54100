// Comic Card V2 Container - Modern Color Scheme
.comic-card-v2 {
  @apply relative w-full flex bg-neutral-50 border border-gray-200 dark:border-neutral-700 dark:bg-neutral-800 rounded-xl h-36 overflow-hidden dark:text-neutral-50;
}

// Image Container
.comic-image-container {
  @apply h-full w-[134px] p-2 overflow-hidden flex;
}

.comic-image-v2 {
  @apply shadow-lg object-cover h-full rounded-lg hover:brightness-90;
}

// Content Section
.comic-content {
  @apply flex flex-col pl-2 pr-4 py-1.5 w-full;
}

// Header Section
.comic-header {
  @apply w-full flex justify-between;
}

.comic-title-link {
  @apply font-bold uppercase text-sm text-neutral-800 dark:text-neutral-100;

  &:hover {
    @apply text-primary-100;
  }
}

.comic-title-v2 {
  @apply line-clamp-1;
}

.comic-update-time {
  @apply text-xs text-neutral-500 dark:text-neutral-400 flex-shrink-0;
}

// Genre Tags - Enhanced Colors
.genre-tags {
  @apply flex space-x-1;
}

.genre-tag {
  @apply text-pretty;
}

.genre-tag-primary {
  .genre-tag-text {
    @apply text-nowrap bg-primary-100 cursor-pointer text-xs font-bold rounded-md shadow-md px-2 uppercase text-white;
  }
}

.genre-tag-secondary {
  .genre-tag-text {
    @apply text-nowrap bg-neutral-100 text-[0.7rem] dark:bg-neutral-700 dark:text-neutral-200 cursor-pointer font-semibold rounded-md shadow-sm px-2 uppercase text-neutral-700;
    &:hover {
      @apply bg-neutral-200 dark:bg-neutral-600 shadow-md;
    }
  }
}

// Stats Section - Modern Colors
.comic-stats {
  @apply flex flex-col-reverse lg:flex-row gap-1 lg:gap-3;
}

.stats-row {
  @apply text-sm text-center flex gap-2 items-center text-neutral-600 dark:text-neutral-300;
}

.rating-stat {
  @apply flex gap-1 items-center text-amber-500 dark:text-amber-400;
}

.bookmark-stat {
  @apply flex gap-1 items-center;
}

.view-stat {
  @apply uppercase flex gap-1 items-center;
}


// Status Section - Enhanced Colors
.status-container {
  @apply text-sm text-center flex gap-2 items-center;
}

.status-ongoing {
  @apply flex gap-2 items-center;
}

.status-indicator.ongoing {
  @apply animate-ping h-1 w-1 rounded-full bg-sky-400 opacity-75;
}

.status-completed {
  @apply flex gap-2 items-center;
}

.status-icon.completed {
  @apply animate-ping opacity-75;
}

.status-text {
  @apply text-sm;
}

// Description Section - Improved Readability
.comic-description {
  @apply mt-1 text-neutral-800 dark:text-neutral-300 text-xs;
}

.description-text {
  @apply line-clamp-3 text-sm leading-relaxed;
}

// Placeholder Styles - Modern Skeleton Loading
.placeholder-container {
  @apply relative w-full flex rounded-xl h-44 overflow-hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.placeholder-image-section {
  @apply w-1/3 h-full animate-pulse bg-neutral-200 dark:bg-neutral-700 relative rounded-lg flex justify-center items-center;
}

.placeholder-icon {
  @apply w-10 h-10 text-neutral-300 dark:text-neutral-500;
}

.placeholder-content-section {
  @apply relative w-full flex bg-neutral-50 dark:bg-neutral-800 rounded-xl h-44 overflow-hidden animate-pulse;
}

.placeholder-image-area {
  @apply flex-shrink-0 h-full p-2 overflow-hidden;
}

.placeholder-image-block {
  @apply bg-neutral-200 dark:bg-neutral-700 w-full h-full rounded-lg;
}

.placeholder-text-area {
  @apply flex flex-col pl-2 pr-4 py-1 w-full;
}

.placeholder-title {
  @apply bg-neutral-200 dark:bg-neutral-700 h-6 my-1 rounded-lg;
}

.placeholder-tags {
  @apply flex space-x-1;
}

.placeholder-tag {
  @apply bg-neutral-200 dark:bg-neutral-700 h-4 w-16 rounded-lg;
}

.placeholder-description {
  @apply bg-neutral-200 dark:bg-neutral-700 h-20 w-full mt-2 rounded-lg;
}