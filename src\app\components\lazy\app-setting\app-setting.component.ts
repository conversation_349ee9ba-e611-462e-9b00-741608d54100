import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, PLATFORM_ID, computed, signal } from '@angular/core';
import { Selection2Component } from '@components/common/selection-2/selection-2.component';
import { InputType } from '@schema';
import { SettingService } from '@services/setting.service';
import { ToastService } from '@services/toast.service';
import { IPopupComponent } from 'src/app/core/interface';
import { OptimizedBaseComponent } from '../../common/base/optimized-base.component';
import { SettingRangeComponent } from './components/setting-range/setting-range.component';
import { SettingToggleComponent } from './components/setting-toggle/setting-toggle.component';
import {
  EnhancedSettingOption,
  SettingCategory,
  SettingTab
} from './interfaces/setting-interfaces';

@Component({
  selector: 'div[app-app-setting]',
  standalone: true,
  templateUrl: './app-setting.component.html',
  styleUrl: './app-setting.component.scss',
  imports: [
    CommonModule,
    Selection2Component,
    SettingToggleComponent,
    SettingRangeComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppSettingComponent extends OptimizedBaseComponent implements IPopupComponent {


  // Signals for reactive state management
  private readonly isVisibleSignal = signal<boolean>(false);
  private readonly selectedTabSignal = signal<SettingCategory>(SettingCategory.APPEARANCE);
  private readonly searchTermSignal = signal<string>('');

  // Computed properties
  readonly isVisible = computed(() => this.isVisibleSignal());
  readonly selectedTab = computed(() => this.selectedTabSignal());
  readonly searchTerm = computed(() => this.searchTermSignal());

  readonly settingGroups = computed(() => this.settingService.settingGroups());
  readonly filteredGroups = computed(() => {
    const groups = this.settingGroups();
    const search = this.searchTerm().toLowerCase();

    if (!search) return groups;

    return groups.map(group => ({
      ...group,
      settings: group.settings.filter(setting =>
        setting.name.toLowerCase().includes(search) ||
        setting.description?.toLowerCase().includes(search)
      )
    })).filter(group => group.settings.length > 0);
  });

  readonly settingTabs = computed((): SettingTab[] => [
    {
      id: 'appearance',
      name: 'Giao diện',
      icon: 'palette',
      category: SettingCategory.APPEARANCE
    },
    {
      id: 'reading',
      name: 'Đọc truyện',
      icon: 'book-open',
      category: SettingCategory.READING
    },
    {
      id: 'behavior',
      name: 'Hành vi',
      icon: 'behavior',
      category: SettingCategory.BEHAVIOR
    },
    // {
    //   id: 'accessibility',
    //   name: 'Trợ năng',
    //   icon: 'accessibility',
    //   category: SettingCategory.ACCESSIBILITY
    // },
    // {
    //   id: 'advanced',
    //   name: 'Nâng cao',
    //   icon: 'tool',
    //   category: SettingCategory.ADVANCED
    // }
  ]);

  readonly currentGroup = computed(() => {
    const groups = this.filteredGroups();
    return groups.find(group => group.category === this.selectedTab());
  });

  // TrackBy functions for performance
  trackByTabId = (_index: number, tab: SettingTab): string => tab.id;
  trackBySettingId = (_index: number, setting: EnhancedSettingOption): string => setting.id;

  // Legacy properties for backward compatibility
  selectedGroup = 1;

  constructor(
    public settingService: SettingService,
    protected override cd: ChangeDetectorRef,
    private toastService: ToastService,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  // Enhanced methods
  setVisible(isVisible: boolean): void {
    this.isVisibleSignal.set(isVisible);
  }

  show(object: any): Promise<any> {
    return new Promise((resolve) => {
      const category = object.selectedGroup as SettingCategory;
      this.open(category);
      const interval = setInterval(() => {
        if (!this.isVisible()) {
          clearInterval(interval);
          resolve({ selectedGroup: this.selectedGroup });
        }
      }, 100);
    });
  }

  public open(category: SettingCategory = SettingCategory.APPEARANCE): void {
    this.selectedTabSignal.set(category);
    this.setVisible(true);
  }

  public close(): void {
    this.setVisible(false);
  }

  selectTab(category: SettingCategory): void {
    this.selectedTabSignal.set(category);
  }

  onSearchChange(searchTerm: string): void {
    this.searchTermSignal.set(searchTerm);

  }

  onSettingChange(setting: EnhancedSettingOption, newValue: any): void {
    // console.log('Setting changed:', setting.id, newValue);
    this.settingService.setSettingValue(setting.id, newValue);
  }

  resetToDefaults(): void {
    const currentGroup = this.currentGroup();
    if (currentGroup) {
      currentGroup.settings.forEach(setting => {
        this.settingService.setSettingValue(setting.id, setting.defaultValue);
      });
    }
  }

  getInputType(setting: EnhancedSettingOption): string {
    switch (setting.inputType) {
      case InputType.Toggle:
        return 'toggle';
      case InputType.Range:
        return 'range';
      case InputType.Selection:
        return 'selection';
      case InputType.Color:
        return 'color';
      default:
        return 'text';
    }
  }

  getTabIcon(iconName: string): string {
    const icons: Record<string, string> = {
      'palette': `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="13.5" cy="6.5" r=".5"/>
        <circle cx="17.5" cy="10.5" r=".5"/>
        <circle cx="8.5" cy="7.5" r=".5"/>
        <circle cx="6.5" cy="12.5" r=".5"/>
        <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
      </svg>`,
      'book-open': `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
      </svg>`,
      'accessibility': `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="4" r="2"/>
        <path d="M10.5 8.5L8 12l2 7h4l2-7-2.5-3.5"/>
        <path d="M7 12h10"/>
        <path d="M8 16l-2 2"/>
        <path d="M16 16l2 2"/>
      </svg>`,
      'tool': `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
      </svg>`
    };
    return icons[iconName] || icons['settings'];
  }

  private getCategoryFromGroup(group: number): SettingCategory {
    const categoryMap = {
      1: SettingCategory.APPEARANCE,
      2: SettingCategory.READING,
      3: SettingCategory.BEHAVIOR,
      // 4: SettingCategory.ACCESSIBILITY,
      // 5: SettingCategory.ADVANCED
    };
    return categoryMap[group as keyof typeof categoryMap] || SettingCategory.APPEARANCE;
  }

  // Legacy methods for backward compatibility
  selectOption(option: { label: string; value: number }): void {
    this.selectedGroup = option.value;
    const category = this.getCategoryFromGroup(option.value);
    this.selectTab(category);
  }

}
