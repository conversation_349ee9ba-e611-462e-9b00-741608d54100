import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Inject,
  OnDestroy,
  PLATFORM_ID,
  ViewChild
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { EmptyComponent } from '@components/common/empty/empty.component';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic, IServiceResponse } from '@schema';
import { ComicService } from '@services/comic.service';
import { UrlService } from '@services/url.service';
import { Subject, debounceTime, distinctUntilChanged, of, switchMap } from 'rxjs';

@Component({
  selector: 'div[app-list-search-comic]',
  templateUrl: './list-search-comic.component.html',
  styleUrl: './list-search-comic.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, RouterLink, EmptyComponent, NumeralPipe],
})
export class ListSearchComicComponent extends OptimizedBaseComponent implements OnDestroy {

  // Component state
  listSearch: Comic[] = [];
  isSearching = false;
  searchText = '';
  isLoading = false;
  skeletonLoader = new Array(1);

  // Performance optimizations
  private searchSubject = new Subject<string>();
  private readonly SEARCH_DEBOUNCE_TIME = 500;

  @ViewChild('SearchInput') SearchInput!: ElementRef;
  @ViewChild('SearchFrame') SearchFrame!: ElementRef;

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private comicService: ComicService,
    private urlService: UrlService
  ) {
    super(cdr, platformId);
    this.setupSearchHandler();
  }
  getComicUrl(comic: Comic): any {
    return this.urlService.getComicDetailUrl(comic);
  }
  // Computed properties
  get hasResults(): boolean {
    return this.listSearch.length > 0;
  }

  get showResults(): boolean {
    return this.isSearching && (this.hasResults || this.isLoading);
  }

  // TrackBy function for ngFor optimization
  trackByComicId = (_index: number, comic: Comic): number => {
    return comic.id;
  };

  private setupSearchHandler(): void {
    this.addSubscription(
      this.searchSubject
        .pipe(
          debounceTime(this.SEARCH_DEBOUNCE_TIME),
          distinctUntilChanged(),
          switchMap((searchTerm: string) => {
            if (searchTerm.trim()) {
              return this.comicService.getSearchComic(searchTerm);
            }
            return of({ data: [], status: 200, message: '' } as IServiceResponse<Comic[]>);
          })
        )
        .subscribe((res: IServiceResponse<Comic[]>) => {
          this.listSearch = res.data || [];
          this.isLoading = false;
          this.safeMarkForCheck();
        })
    );
  }

  SendSearchReq(): void {
    if (this.searchText.trim()) {
      this.isLoading = true;
      this.searchSubject.next(this.searchText);
    } else {
      this.listSearch = [];
      this.isLoading = false;
      this.safeMarkForCheck();
    }
  }

  OnSearchChange(): void {
    this.searchText = this.SearchInput.nativeElement.value;
    this.SendSearchReq();
  }
  OnSearchFocus = (isFocus: boolean): boolean => {
    this.isSearching = isFocus;
    this.runInBrowser(() => {
      if (isFocus) {
        this.SearchInput.nativeElement.classList.add('!w-full');
      } else {
        this.SearchFrame.nativeElement.classList.add('hidden');
        this.SearchInput.nativeElement.classList.remove('!w-full');
      }
    });
    return true;
  };

  OnSearchClick = (): boolean => {
    this.isSearching = true;

    this.runInBrowser(() => {
      this.SearchFrame.nativeElement.classList.remove('hidden');
      this.SearchInput.nativeElement.focus();
    });
    return true;
  };

  clearSearch(): void {
    this.searchText = '';
    this.runInBrowser(() => {
      this.SearchInput.nativeElement.value = '';
    });
    this.listSearch = [];
    this.safeMarkForCheck();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }
}
