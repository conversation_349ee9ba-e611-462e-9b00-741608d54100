// ===== SYNC PROGRESS COMPONENT =====
// Animated progress tracking with beautiful UI

// ===== CONTAINER =====
.sync-progress-container {
  @apply bg-white dark:bg-neutral-800 rounded-2xl p-6 
         border border-gray-200 dark:border-neutral-700 
         shadow-lg transition-all duration-500;

  &.active {
    @apply border-blue-300 dark:border-blue-700 shadow-blue-100 dark:shadow-blue-900/20;
  }

  &.completed {
    @apply border-green-300 dark:border-green-700 shadow-green-100 dark:shadow-green-900/20;
  }

  &.error {
    @apply border-red-300 dark:border-red-700 shadow-red-100 dark:shadow-red-900/20;
  }
}

// ===== PROGRESS HEADER =====
.progress-header {
  @apply flex items-center gap-4 mb-6;
}

.progress-icon {
  @apply w-12 h-12 rounded-xl flex items-center justify-center 
         transition-all duration-300;

  &[data-color="gray"] {
    @apply bg-gray-100 dark:bg-neutral-700;
  }

  &[data-color="blue"] {
    @apply bg-blue-100 dark:bg-blue-900/30;
  }

  &[data-color="green"] {
    @apply bg-green-100 dark:bg-green-900/30;
  }

  &[data-color="orange"] {
    @apply bg-orange-100 dark:bg-orange-900/30;
  }

  &[data-color="purple"] {
    @apply bg-purple-100 dark:bg-purple-900/30;
  }

  &[data-color="red"] {
    @apply bg-red-100 dark:bg-red-900/30;
  }
}

.icon {
  @apply w-6 h-6 transition-all duration-300;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;

  .progress-icon[data-color="gray"] & {
    @apply text-gray-600 dark:text-gray-400;
  }

  .progress-icon[data-color="blue"] & {
    @apply text-blue-600 dark:text-blue-400;
  }

  .progress-icon[data-color="green"] & {
    @apply text-green-600 dark:text-green-400;
  }

  .progress-icon[data-color="orange"] & {
    @apply text-orange-600 dark:text-orange-400;
  }

  .progress-icon[data-color="purple"] & {
    @apply text-purple-600 dark:text-purple-400;
  }

  .progress-icon[data-color="red"] & {
    @apply text-red-600 dark:text-red-400;
  }
}

.progress-info {
  @apply flex-1 space-y-1;
}

.progress-title {
  @apply text-lg font-semibold text-gray-900 dark:text-light-text;
}

.progress-meta {
  @apply flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400;
}

.current-site {
  @apply px-2 py-1 bg-blue-100 dark:bg-blue-900/30 
         text-blue-700 dark:text-blue-300 rounded-md font-medium;
}

.elapsed-time {
  @apply font-mono;
}

.progress-percentage {
  @apply text-right;
}

.percentage-text {
  @apply text-2xl font-bold text-gray-900 dark:text-light-text;
}

// ===== PROGRESS BAR =====
.progress-bar-container {
  @apply mb-6;
}

.progress-bar {
  @apply w-full h-3 bg-gray-200 dark:bg-neutral-700 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full transition-all duration-500 ease-out;

  &[data-color="gray"] {
    @apply bg-gray-400;
  }

  &[data-color="blue"] {
    @apply bg-gradient-to-r from-blue-500 to-blue-600;
  }

  &[data-color="green"] {
    @apply bg-gradient-to-r from-green-500 to-green-600;
  }

  &[data-color="orange"] {
    @apply bg-gradient-to-r from-orange-500 to-orange-600;
  }

  &[data-color="purple"] {
    @apply bg-gradient-to-r from-purple-500 to-purple-600;
  }

  &[data-color="red"] {
    @apply bg-gradient-to-r from-red-500 to-red-600;
  }
}

// ===== PROGRESS STAGES =====
.progress-stages {
  @apply mb-6;
}

.stages-container {
  @apply flex items-start justify-between;
}

.stage-item {
  @apply flex flex-col items-center space-y-2 relative;
}

.stage-indicator {
  @apply flex items-center;
}

.stage-circle {
  @apply w-8 h-8 rounded-full border-2 flex items-center justify-center 
         transition-all duration-300;

  .stage-item.pending & {
    @apply border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-800;
  }

  .stage-item.active & {
    @apply border-blue-500 bg-blue-50 dark:bg-blue-900/30;
  }

  .stage-item.completed & {
    @apply border-green-500 bg-green-500;
  }
}

.stage-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;

  &.completed {
    @apply text-white;
  }
}

.stage-number {
  @apply text-xs font-semibold;

  .stage-item.pending & {
    @apply text-gray-500 dark:text-gray-400;
  }

  .stage-item.active & {
    @apply text-blue-600 dark:text-blue-400;
  }
}

.stage-connector {
  @apply w-16 h-0.5 ml-2 transition-all duration-300;

  .stage-item.completed & {
    @apply bg-green-500;
  }

  .stage-item.active & {
    @apply bg-blue-500;
  }

  .stage-item.pending & {
    @apply bg-gray-300 dark:bg-neutral-600;
  }
}

.stage-label {
  @apply text-xs font-medium text-center transition-all duration-300;

  .stage-item.pending & {
    @apply text-gray-500 dark:text-gray-400;
  }

  .stage-item.active & {
    @apply text-blue-600 dark:text-blue-400;
  }

  .stage-item.completed & {
    @apply text-green-600 dark:text-green-400;
  }
}

// ===== CURRENT ACTION =====
.current-action {
  @apply flex items-center gap-3 p-4 
         bg-blue-50 dark:bg-blue-900/20 
         border border-blue-200 dark:border-blue-800 
         rounded-lg mb-6;
}

.action-indicator {
  @apply relative;
}

.action-spinner {
  @apply w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.action-text {
  @apply text-sm font-medium text-blue-700 dark:text-blue-300;
}

// ===== ACTION BUTTONS =====
.progress-actions {
  @apply flex gap-3 justify-end;
}

.action-button {
  @apply flex items-center gap-2 px-4 py-2 
         font-medium rounded-lg 
         transition-all duration-200;

  &.cancel {
    @apply bg-red-50 hover:bg-red-100 
           dark:bg-red-900/20 dark:hover:bg-red-900/30 
           text-red-600 dark:text-red-400 
           border border-red-200 dark:border-red-800;
  }

  &.retry {
    @apply bg-blue-50 hover:bg-blue-100 
           dark:bg-blue-900/20 dark:hover:bg-blue-900/30 
           text-blue-600 dark:text-blue-400 
           border border-blue-200 dark:border-blue-800;
  }
}

.button-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.button-text {
  @apply text-sm;
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .sync-progress-container {
    @apply p-4;
  }

  .progress-header {
    @apply flex-col items-start gap-3;
  }

  .progress-percentage {
    @apply text-left;
  }

  .stages-container {
    @apply flex-col space-y-4;
  }

  .stage-item {
    @apply flex-row items-center space-y-0 space-x-3;
  }

  .stage-connector {
    @apply hidden;
  }

  .progress-actions {
    @apply flex-col;
  }

  .action-button {
    @apply w-full justify-center;
  }
}
