<div
  *ngIf="isVisible"
  class="report-overlay"
>
  <div class="report-modal">
    <!-- Header -->
    <div class="report-header">
      <div class="header-content">
        <h3 class="header-title">Báo cáo lỗi</h3>
        <p class="header-subtitle"><PERSON><PERSON><PERSON> thông báo lỗi để team cải thiện nhé!</p>
      </div>
      <button
        class="report-close"
        (click)="this.setVisible(false);"
        aria-label="Đóng"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    </div>
    <!-- Content -->
    <div class="report-content">
      <form
        [formGroup]="errorForm"
        (ngSubmit)="sendReportError()"
        class="report-form"
      >
        <!-- Error Type Field -->
        <div class="form-group">
          <label for="errorType" class="form-label">
            <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="12"/>
              <line x1="12" y1="16" x2="12.01" y2="16"/>
            </svg>
            Loại lỗi
          </label>
          <input
            id="errorType"
            formControlName="errorType"
            type="text"
            maxlength="100"
            class="form-input"
            placeholder="Nhập loại lỗi gặp phải..."
          />

          <!-- Error Messages -->
          <div
            *ngIf="
              errorForm.controls['errorType'].errors?.['required'] &&
              errorForm.controls['errorType'].touched
            "
            class="error-message"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
            Vui lòng nhập thông tin lỗi
          </div>
          <div
            *ngIf="
              errorForm.controls['errorType'].errors?.['maxlength'] &&
              errorForm.controls['errorType'].touched
            "
            class="error-message"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
            Không được vượt quá 100 ký tự
          </div>
        </div>
        <!-- Quick Tags -->
        <div class="quick-tags">
          <p class="tags-label">Hoặc chọn nhanh:</p>
          <div class="tags-container">
            <button
              type="button"
              *ngFor="let tag of defaultType"
              (click)="setErrorType(tag)"
              class="tag-button"
            >
              {{ tag }}
            </button>
          </div>
        </div>
        <!-- Description Field -->
        <div class="form-group">
          <label for="message" class="form-label">
            <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
            Mô tả chi tiết
          </label>
          <textarea
            id="message"
            maxlength="1000"
            formControlName="message"
            class="form-textarea"
            placeholder="Mô tả chi tiết về lỗi bạn gặp phải..."
            rows="4"
          ></textarea>
          <div
            *ngIf="
              errorForm.controls['message'].errors?.['maxlength'] &&
              errorForm.controls['message'].touched
            "
            class="error-message"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
            Không được vượt quá 1000 ký tự
          </div>
        </div>

        <!-- Actions -->
        <div class="form-actions">
          <button
            class="action-button action-button--primary"
            type="submit"
            [disabled]="!errorForm.valid"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M22 2L11 13"/>
              <polygon points="22,2 15,22 11,13 2,9"/>
            </svg>
            Gửi báo cáo
          </button>
          <button
            type="button"
            class="action-button action-button--secondary"
            (click)="this.setVisible(false);"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
            Hủy bỏ
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
