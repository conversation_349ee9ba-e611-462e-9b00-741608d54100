import { Directive, ElementRef, EventEmitter, HostListener, Output } from '@angular/core';

@Directive({
    selector: '[appClickOutside]',
    standalone: true
})
export class ClickOutsideDirective {
  @Output() appClickOutside = new EventEmitter<void>();

  constructor(private el: ElementRef) { }

  // Lắng nghe sự kiện click trong document
  @HostListener('document:click', ['$event.target']) onClick(targetElement: HTMLElement) {

    const clickedInside = this.el.nativeElement.contains(targetElement);
    if (!clickedInside) {
      this.appClickOutside.emit(); // <PERSON><PERSON>t ra sự kiện khi click ra ngoài
    }
  }

}
