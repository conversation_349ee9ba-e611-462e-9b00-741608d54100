import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LoginFormComponent } from './login-form/login-form.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { RegisterFormComponent } from './register-form/register-form.component';
import { ConfirmEmailComponent } from './confirm-email/confirm-email.component';
const routes: Routes = [

  {
    path: '',
    children: [
      {
        path: 'login',
        component: LoginFormComponent,
      },
      {
        path: 'register',
        component: RegisterFormComponent,
      },
      {
        path: 'confirm-email',
        component: ConfirmEmailComponent,
      },
      {
        path: 'forgot-password',
        component: ForgotPasswordComponent,
      }
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AuthRoutingModule { }
