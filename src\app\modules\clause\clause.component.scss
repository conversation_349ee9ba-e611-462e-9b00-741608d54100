// Terms of Service Container
.terms-container {
  @apply max-w-6xl mx-auto px-4 py-8 bg-white dark:bg-dark-bg text-neutral-900 dark:text-light-text;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
}

// Header Section
.terms-header {
  @apply text-center mb-12 py-16 bg-gradient-to-br from-orange-50 to-red-100 dark:from-neutral-800 dark:to-neutral-900 rounded-2xl;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.1) 0%, rgba(239, 68, 68, 0.1) 100%);
    pointer-events: none;
  }
}

.terms-header-content {
  @apply relative z-10;
}

.terms-title {
  @apply text-4xl md:text-5xl font-bold mb-4 text-neutral-900 dark:text-light-text flex items-center justify-center gap-4;
  background: linear-gradient(135deg, #f97316, #ef4444);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.terms-icon {
  @apply w-12 h-12 text-orange-600 dark:text-orange-400;
}

.terms-subtitle {
  @apply text-xl text-neutral-600 dark:text-neutral-300 mb-6 max-w-2xl mx-auto;
}

.terms-meta {
  @apply flex flex-wrap justify-center gap-6 text-sm text-neutral-500 dark:text-neutral-400;
}

.terms-date,
.terms-version {
  @apply px-4 py-2 bg-white dark:bg-neutral-800 rounded-full border border-neutral-200 dark:border-neutral-700;
}

// Table of Contents
.terms-toc {
  @apply mb-12 p-6 bg-neutral-50 dark:bg-neutral-800 rounded-xl border border-neutral-200 dark:border-neutral-700;
}

.toc-title {
  @apply text-2xl font-bold mb-6 text-neutral-900 dark:text-light-text;
}

.toc-list {
  @apply grid grid-cols-1 md:grid-cols-2 gap-3;
  counter-reset: toc-counter;
}

.toc-link {
  @apply block p-3 text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20 rounded-lg transition-all duration-200 font-medium;

  &:hover {
    transform: translateX(4px);
  }
}

// Main Content
.terms-content {
  @apply space-y-16;
}

// Section Styling
.terms-section {
  @apply scroll-mt-24;

  &:not(:last-child) {
    @apply border-b border-neutral-200 dark:border-neutral-700 pb-16;
  }
}

.section-title {
  @apply text-3xl font-bold mb-8 text-neutral-900 dark:text-light-text flex items-center gap-4;
}

.section-number {
  @apply inline-flex items-center justify-center w-12 h-12 bg-orange-600 text-white rounded-full text-lg font-bold;
}

.section-content {
  @apply space-y-6;
}

.section-intro {
  @apply text-lg text-neutral-700 dark:text-neutral-300 font-medium;
}

.subsection-title {
  @apply text-xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200;
}

// Important Notice
.important-notice {
  @apply p-6 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 rounded-r-lg;

  h3 {
    @apply text-lg font-semibold mb-3 text-red-800 dark:text-red-300;
  }

  p {
    @apply text-red-700 dark:text-red-300;
  }
}

// Age Requirements
.age-requirements {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.age-card {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-center hover:shadow-lg transition-all duration-300;

  &:hover {
    transform: translateY(-2px);
  }

  h4 {
    @apply text-lg font-semibold mb-3 text-neutral-900 dark:text-light-text;
  }

  p {
    @apply text-neutral-600 dark:text-neutral-300;
  }
}

// Service Grid
.service-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.service-card {
  @apply p-6 bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-800 dark:to-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded-xl hover:shadow-lg transition-all duration-300;

  &:hover {
    transform: translateY(-4px);
  }
}

.service-icon {
  @apply text-4xl mb-4 block;
}

.service-card h4 {
  @apply text-lg font-semibold mb-3 text-neutral-900 dark:text-light-text;
}

.service-card p {
  @apply text-neutral-600 dark:text-neutral-300 mb-4;
}

.service-card ul {
  @apply space-y-2;

  li {
    @apply flex items-start gap-2 text-sm text-neutral-600 dark:text-neutral-300;

    &::before {
      content: '✓';
      @apply text-lime-600 dark:text-lime-400 font-bold mt-1 flex-shrink-0;
    }
  }
}

// Availability Info
.availability-info {
  @apply p-6 bg-sky-50 dark:bg-sky-900/20 border border-sky-200 dark:border-sky-800 rounded-xl;

  p {
    @apply text-sky-700 dark:text-sky-300 mb-4;
  }

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2;

      &::before {
        content: '⚠️';
        @apply mt-1 flex-shrink-0;
      }
    }
  }
}

// Account Requirements
.account-requirements {
  @apply p-6 bg-lime-50 dark:bg-lime-900/20 border border-lime-200 dark:border-lime-800 rounded-xl;

  h4 {
    @apply text-lg font-semibold mb-3 text-lime-800 dark:text-lime-300;
  }

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2;

      &::before {
        content: '📝';
        @apply mt-1 flex-shrink-0;
      }
    }
  }
}

// Security Grid
.security-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.security-item {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl;

  h4 {
    @apply text-lg font-semibold mb-3 text-neutral-900 dark:text-light-text;
  }

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2 text-neutral-600 dark:text-neutral-300;

      &::before {
        content: '•';
        @apply text-orange-500 font-bold mt-1 flex-shrink-0;
      }
    }
  }
}

// Rights and Obligations
.rights-obligations-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-8;
}

.rights-section,
.obligations-section {
  @apply p-6 border border-neutral-200 dark:border-neutral-700 rounded-xl;
}

.rights-section {
  @apply bg-lime-50 dark:bg-lime-900/20 border-lime-200 dark:border-lime-800;

  h3 {
    @apply text-lg font-semibold mb-4 text-lime-800 dark:text-lime-300;
  }
}

.obligations-section {
  @apply bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800;

  h3 {
    @apply text-lg font-semibold mb-4 text-orange-800 dark:text-orange-300;
  }
}

.rights-section ul,
.obligations-section ul {
  @apply space-y-3;

  li {
    @apply flex items-start gap-3;

    &::before {
      content: '✓';
      @apply text-lime-600 dark:text-lime-400 font-bold mt-1 flex-shrink-0;
    }
  }
}

.obligations-section ul li::before {
  content: '📋';
}

// Content Notice
.content-notice {
  @apply p-6 bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-500 rounded-r-lg;

  h4 {
    @apply text-lg font-semibold mb-3 text-yellow-800 dark:text-yellow-300;
  }

  p {
    @apply text-yellow-700 dark:text-yellow-300;
  }
}

// Copyright Policy
.copyright-policy {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.policy-item {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl;

  h4 {
    @apply text-lg font-semibold mb-3 text-neutral-900 dark:text-light-text;
  }

  p {
    @apply text-neutral-600 dark:text-neutral-300;
  }
}

// Prohibited Conduct
.prohibited-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.prohibited-category {
  @apply p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl;

  h3 {
    @apply text-lg font-semibold mb-4 text-red-800 dark:text-red-300;
  }

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2 text-red-700 dark:text-red-300;

      &::before {
        content: '❌';
        @apply mt-1 flex-shrink-0;
      }
    }
  }
}

// Violation Consequences
.violation-consequences {
  @apply mt-8;

  h3 {
    @apply text-lg font-semibold mb-6 text-neutral-900 dark:text-light-text;
  }
}

.consequence-levels {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.level-card {
  @apply p-6 border border-neutral-200 dark:border-neutral-700 rounded-xl text-center;

  &.warning {
    @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800;

    h4 {
      @apply text-yellow-800 dark:text-yellow-300;
    }

    p {
      @apply text-yellow-700 dark:text-yellow-300;
    }
  }

  &.suspension {
    @apply bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800;

    h4 {
      @apply text-orange-800 dark:text-orange-300;
    }

    p {
      @apply text-orange-700 dark:text-orange-300;
    }
  }

  &.permanent {
    @apply bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800;

    h4 {
      @apply text-red-800 dark:text-red-300;
    }

    p {
      @apply text-red-700 dark:text-red-300;
    }
  }

  h4 {
    @apply text-lg font-semibold mb-2;
  }

  p {
    @apply text-sm;
  }
}

// Liability and Limitations
.liability-notice {
  @apply p-6 bg-neutral-50 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl;

  p {
    @apply text-neutral-700 dark:text-neutral-300 font-medium;
  }
}

.warranty-exclusions {
  @apply p-6 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl;

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2 text-orange-700 dark:text-orange-300;

      &::before {
        content: '⚠️';
        @apply mt-1 flex-shrink-0;
      }
    }
  }
}

// Payment and Service Info
.free-service-info,
.premium-info {
  @apply p-6 bg-lime-50 dark:bg-lime-900/20 border border-lime-200 dark:border-lime-800 rounded-xl;

  p {
    @apply text-lime-700 dark:text-lime-300;
  }
}

.premium-info {
  @apply bg-sky-50 dark:bg-sky-900/20 border-sky-200 dark:border-sky-800;

  p {
    @apply text-sky-700 dark:text-sky-300;
  }
}

// Information Security
.policy-link {
  @apply text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300 underline font-medium;
}

.info-collection {
  @apply p-6 bg-sky-50 dark:bg-sky-900/20 border border-sky-200 dark:border-sky-800 rounded-xl;

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2 text-sky-700 dark:text-sky-300;

      &::before {
        content: '📊';
        @apply mt-1 flex-shrink-0;
      }
    }
  }
}

// Notification Process
.notification-process {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.process-step {
  @apply flex flex-col items-center text-center;
}

.step-number {
  @apply w-12 h-12 bg-orange-600 text-white rounded-full flex items-center justify-center font-bold text-lg mb-4;
}

.step-content {
  h4 {
    @apply font-semibold mb-2 text-neutral-900 dark:text-light-text;
  }

  p {
    @apply text-sm text-neutral-600 dark:text-neutral-300;
  }
}

// Contact Methods
.contact-methods {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.contact-card {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl;

  h4 {
    @apply text-lg font-semibold mb-4 text-neutral-900 dark:text-light-text;
  }

  p {
    @apply text-sm text-neutral-600 dark:text-neutral-300 mb-2;

    strong {
      @apply text-neutral-900 dark:text-light-text;
    }
  }
}

// Dispute Resolution
.dispute-resolution {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.resolution-step {
  @apply p-6 bg-sky-50 dark:bg-sky-900/20 border border-sky-200 dark:border-sky-800 rounded-xl;

  h4 {
    @apply text-lg font-semibold mb-3 text-sky-800 dark:text-sky-300;
  }

  p {
    @apply text-sky-700 dark:text-sky-300;
  }
}

// Response Times
.response-times {
  @apply p-6 bg-neutral-50 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl;

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2;

      &::before {
        content: '⏰';
        @apply mt-1 flex-shrink-0;
      }

      strong {
        @apply text-neutral-900 dark:text-light-text;
      }
    }
  }
}

// Footer
.terms-footer {
  @apply mt-16 pt-12 border-t border-neutral-200 dark:border-neutral-700;
}

.footer-content {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-8;
}

.footer-summary {
  h3 {
    @apply text-lg font-semibold mb-4 text-neutral-900 dark:text-light-text;
  }
}

.summary-grid {
  @apply grid grid-cols-2 gap-4;
}

.summary-item {
  @apply flex items-center gap-3 p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg;
}

.summary-icon {
  @apply text-xl;
}

.footer-meta {
  @apply text-center lg:text-right;

  p {
    @apply text-sm text-neutral-500 dark:text-neutral-400 mb-2;
  }
}

.footer-links {
  @apply flex flex-wrap justify-center lg:justify-end gap-4 mt-4;
}

.footer-link {
  @apply text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300 text-sm font-medium;
}

// Responsive Design
@media (max-width: 768px) {
  .terms-title {
    @apply text-3xl flex-col gap-2;
  }

  .terms-meta {
    @apply flex-col gap-3;
  }

  .toc-list {
    @apply grid-cols-1;
  }

  .section-title {
    @apply text-2xl flex-col items-start gap-2;
  }

  .service-grid {
    @apply grid-cols-1;
  }

  .age-requirements {
    @apply grid-cols-1;
  }

  .security-grid {
    @apply grid-cols-1;
  }

  .rights-obligations-grid {
    @apply grid-cols-1;
  }

  .prohibited-grid {
    @apply grid-cols-1;
  }

  .consequence-levels {
    @apply grid-cols-1;
  }

  .contact-methods {
    @apply grid-cols-1;
  }

  .dispute-resolution {
    @apply grid-cols-1;
  }

  .summary-grid {
    @apply grid-cols-1;
  }
}

// Print Styles
@media print {
  .terms-container {
    @apply text-black bg-white;
  }

  .terms-header {
    @apply bg-white border border-neutral-300;
  }

  .toc-link {
    @apply text-black;
  }

  .section-number {
    @apply bg-neutral-800;
  }
}

// Animation Enhancements
.terms-container {
  animation: slideInUp 0.5s ease-out;
}

.age-card,
.service-card,
.security-item,
.policy-item,
.contact-card {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Accessibility Improvements
.toc-link:focus-visible,
.policy-link:focus-visible,
.footer-link:focus-visible {
  @apply outline-none ring-2 ring-orange-500 ring-opacity-50;
}

// High Contrast Mode Support
@media (prefers-contrast: high) {
  .age-card,
  .service-card,
  .security-item,
  .policy-item,
  .contact-card {
    @apply border-2 border-black dark:border-white;
  }
}

// Reduced Motion Support
@media (prefers-reduced-motion: reduce) {
  .age-card,
  .service-card,
  .security-item,
  .policy-item,
  .contact-card,
  .toc-link {
    @apply transition-none;

    &:hover {
      transform: none;
    }
  }

  .terms-container {
    animation: none;
  }
}