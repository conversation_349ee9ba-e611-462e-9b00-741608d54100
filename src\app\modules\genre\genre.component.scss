// Genre Page Styles - Modern Comic Website Design
// Using Tailwind CSS with custom enhancements

// Genre Header Section
.genre-header {
  @apply rounded-2xl py-4 border-gray-100 dark:border-gray-600;
}

.genre-info {
  @apply mb-4;
}

.genre-title-section {
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4;
}

.genre-title {
  @apply text-xl uppercase font-extrabold leading-3 min-h-full text-gray-700 dark:text-light-text mt-0.5;
  
}

.genre-description {
  @apply mt-2;
  
  .description-text {
    @apply text-gray-700 dark:text-gray-300 leading-relaxed;
  }
}

// Filter Section
.genre-filters {
  @apply flex flex-wrap gap-3;
}

.filter-summary {
  @apply mb-4;
}

.filter-controls {
  @apply flex gap-4;
}

.filter-group {
  @apply flex flex-col gap-2;
  
  .filter-label {
    @apply text-sm font-medium text-gray-700 dark:text-gray-300;
  }
  
  .filter-select {
    @apply min-w-[160px];
  }
}

// Loading State
.loading-container {
  @apply flex justify-center items-center py-16;
}

.loading-spinner {
  @apply text-center;
  
  .spinner {
    @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full;
    @apply animate-spin mx-auto mb-4;
  }
  
  .loading-text {
    @apply text-gray-600 dark:text-gray-400;
  }
}

// Empty State
.empty-state {
  @apply text-center py-16 px-4;
  
  .empty-icon {
    @apply text-6xl mb-4;
  }
  
  .empty-title {
    @apply text-2xl font-bold text-gray-900 dark:text-light-text mb-2;
  }
  
  .empty-description {
    @apply text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto;
  }
  
  .empty-action {
    @apply bg-blue-600 hover:bg-blue-700 text-white;
    @apply px-6 py-3 rounded-lg font-medium transition-colors;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
}

// Related Genres Section
.related-genres {
  @apply mt-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-2xl;
  
  .related-title {
    @apply text-xl font-bold text-gray-900 dark:text-light-text mb-4;
  }
  
  .related-list {
    @apply flex flex-wrap gap-2;
  }
  
  .related-item {
    @apply bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300;
    @apply px-4 py-2 rounded-full text-sm font-medium;
    @apply border border-gray-200 dark:border-gray-600;
    @apply hover:bg-blue-50 dark:hover:bg-gray-600 hover:text-blue-600 dark:hover:text-blue-400;
    @apply transition-colors duration-200;
    @apply no-underline;
  }
}

// Responsive Design
@media (max-width: 640px) {
  
  .genre-title {
    @apply text-2xl;
    
    .genre-icon {
      @apply text-xl;
    }
  }
  
  .filter-controls {
    @apply gap-3;
  }
  
  .filter-group {
    @apply flex-1;
  }
}

// Dark Mode Enhancements
@media (prefers-color-scheme: dark) {
  .genre-header {
    @apply shadow-lg;
  }
  
  .stats-item {
    @apply shadow-sm;
  }
}




// Focus States for Accessibility
.empty-action:focus,
.related-item:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-800;
}
