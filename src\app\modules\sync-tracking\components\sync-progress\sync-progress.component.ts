import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Subject, takeUntil } from 'rxjs';
import { SyncProgress, SyncStage } from '../../models/sync-tracking.models';
import { SyncTrackingService } from '../../services/sync-tracking.service';

@Component({
  selector: '[app-sync-progress]',
  templateUrl: './sync-progress.component.html',
  styleUrl: './sync-progress.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false
})
export class SyncProgressComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  @Input() showDetails = true;

  // Component state
  private readonly _progress = signal<SyncProgress>({
    stage: SyncStage.IDLE,
    progress: 0,
    message: 'Ready to sync'
  });

  private readonly _startTime = signal<Date | null>(null);
  protected override readonly destroy$ = new Subject<void>();

  // Computed properties
  readonly progress = computed(() => this._progress());
  readonly startTime = computed(() => this._startTime());
  
  readonly isActive = computed(() => {
    const stage = this.progress().stage;
    return stage !== SyncStage.IDLE && stage !== SyncStage.COMPLETED && stage !== SyncStage.ERROR;
  });

  readonly isCompleted = computed(() => {
    return this.progress().stage === SyncStage.COMPLETED;
  });

  readonly hasError = computed(() => {
    return this.progress().stage === SyncStage.ERROR;
  });

  readonly elapsedTime = computed(() => {
    const start = this.startTime();
    if (!start) return 0;
    return Math.floor((Date.now() - start.getTime()) / 1000);
  });

  readonly progressPercentage = computed(() => {
    return Math.max(0, Math.min(100, this.progress().progress));
  });

  readonly stageIcon = computed(() => {
    const stage = this.progress().stage;
    const icons = {
      [SyncStage.IDLE]: 'clock',
      [SyncStage.CONNECTING]: 'wifi',
      [SyncStage.FETCHING]: 'download',
      [SyncStage.COMPARING]: 'compare',
      [SyncStage.SYNCING]: 'sync',
      [SyncStage.COMPLETED]: 'check',
      [SyncStage.ERROR]: 'alert'
    };
    return icons[stage] || 'clock';
  });

  readonly stageColor = computed(() => {
    const stage = this.progress().stage;
    const colors = {
      [SyncStage.IDLE]: 'gray',
      [SyncStage.CONNECTING]: 'blue',
      [SyncStage.FETCHING]: 'green',
      [SyncStage.COMPARING]: 'purple',
      [SyncStage.SYNCING]: 'blue',
      [SyncStage.COMPLETED]: 'green',
      [SyncStage.ERROR]: 'red'
    };
    return colors[stage] || 'gray';
  });

  readonly stages = computed(() => [
    { key: SyncStage.CONNECTING, label: 'Kết nối', completed: this.isStageCompleted(SyncStage.CONNECTING) },
    { key: SyncStage.FETCHING, label: 'NetTruyen', completed: this.isStageCompleted(SyncStage.FETCHING) },
    { key: SyncStage.COMPARING, label: 'So sánh', completed: this.isStageCompleted(SyncStage.COMPARING) },
    { key: SyncStage.SYNCING, label: 'Đồng bộ', completed: this.isStageCompleted(SyncStage.SYNCING) },
    { key: SyncStage.COMPLETED, label: 'Hoàn tất', completed: this.isStageCompleted(SyncStage.COMPLETED) }
  ]);

  constructor(
    private syncService: SyncTrackingService,
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.subscribeToProgress();
  }

  override ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ===== SUBSCRIPTION MANAGEMENT =====

  private subscribeToProgress(): void {
    this.syncService.progress$
      .pipe(takeUntil(this.destroy$))
      .subscribe(progress => {
        this._progress.set(progress);
        
        // Track start time
        if (progress.stage === SyncStage.CONNECTING && !this.startTime()) {
          this._startTime.set(new Date());
        }
        
        // Reset start time on completion or error
        if (progress.stage === SyncStage.COMPLETED || progress.stage === SyncStage.ERROR) {
          // Keep start time for elapsed time calculation
        }
        
        this.safeMarkForCheck();
      });
  }

  // ===== UTILITY METHODS =====

  private isStageCompleted(stage: SyncStage): boolean {
    const currentStage = this.progress().stage;
    const stageOrder = [
      SyncStage.IDLE,
      SyncStage.CONNECTING,
      SyncStage.FETCHING,
      SyncStage.COMPARING,
      SyncStage.SYNCING,
      SyncStage.COMPLETED
    ];
    
    const currentIndex = stageOrder.indexOf(currentStage);
    const targetIndex = stageOrder.indexOf(stage);
    
    return currentIndex > targetIndex || (currentIndex === targetIndex && currentStage !== SyncStage.ERROR);
  }

  getStageClass(stage: SyncStage): string {
    const currentStage = this.progress().stage;
    if (this.isStageCompleted(stage)) return 'completed';
    if (currentStage === stage) return 'active';
    return 'pending';
  }

  formatElapsedTime(seconds: number): string {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }

  // ===== ACTIONS =====

  cancelSync(): void {
    this.syncService.cancelSync().subscribe({
      next: () => {
        console.log('Sync cancelled successfully');
      },
      error: (error) => {
        console.error('Failed to cancel sync:', error);
        // Fallback to reset if cancel fails
        this.syncService.resetSync();
      }
    });
  }

  retrySync(): void {
    // Reset and allow user to start new sync
    this.syncService.resetSync();
  }

  // ===== TRACKBY FUNCTIONS =====

  trackByStageKey = (index: number, stage: any): string => stage.key;
}
