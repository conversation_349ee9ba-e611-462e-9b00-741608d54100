import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import {
  IFilters,
  advancedFiltersOptions,
} from '@components/utils/constants';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';


import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic, ComicStatus, Genre, SortType } from '@schema';
import { ComicService } from '@services/comic.service';
import { GenreService } from '@services/genre.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import { IOption } from 'src/app/dataSource/schema/IOption';

@Component({
  selector: 'main[app-search-page]',
  templateUrl: './search-page.component.html',
  styleUrl: './search-page.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SearchPageComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  listComics: Comic[] = [];
  totalpage!: number;
  totalResult!: number;
  currentPage = 1;
  dataView: IFilters;
  listGenres: Genre[] = [];
  showFilters = false;
  nowyear!: number;
  private isOnInit = false;
  filterTags: any[] = [];
  isLoading = false;
  isSearching = false;
  lastupdate!: SortType.LastUpdate;
  step = 30;
  nPreview = 30;
  queryParams: Params = {};

  // Performance optimizations
  private searchSubject = new Subject<string>();
  private resizeListener?: () => void;

  selectOptions: any = {
    sorts: { value: -1, label: '', isShow: false },
    status: { value: -1, label: '', isShow: false },
    year: { value: 0, label: '', isShow: false },
    genres: { value: {}, label: {}, isShow: false },
    keyword: { value: '', label: '' },
  };


  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private urlService: UrlService,
    override cd: ChangeDetectorRef,
    private genreService: GenreService,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);

    this.dataView = {
      status: advancedFiltersOptions.status,
      sorts: advancedFiltersOptions.sorts,
    };
    this.selectOptions.status.label = advancedFiltersOptions.status[0].label;
    this.selectOptions.sorts.label = advancedFiltersOptions.sorts[0].label;
    this.lastupdate = SortType.LastUpdate;
    this.isLoading = true;
  }


  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  ngOnInit(): void {
    // Initial SEO setup
    this.setupSEO();
    if (!this.isBrowser) return;

    this.addSubscription(
      this.genreService.getGenres()
        .pipe(this.takeUntilDestroy())
        .subscribe((genres: any) => {
          this.listGenres = genres;
        })
    );
    
    this.addSubscription(
      this.route.queryParams.pipe(
        this.takeUntilDestroy()
      ).subscribe((params: any) => {
        const page = +params['page'] || 1;
        const status = +params['status'] || ComicStatus.ALL;
        const sort = +params['sort'] >= 0 ? +params['sort'] : SortType.LastUpdate;
        const genres = params['genres'] || '';
        const nogenres = params['nogenres'] || '';
        const year = +params['year'] || 0;
        const keyword = params['keyword'] || '';
        this.currentPage = page;

        this.resetOptions(status, sort, year, genres, nogenres, keyword);
        this.searchComics(page, sort, status, genres, nogenres, year, keyword);
      })
    );

    // Setup debounced search
    this.addSubscription(
      this.searchSubject.pipe(
        debounceTime(300),
        distinctUntilChanged(),
        this.takeUntilDestroy()
      ).subscribe((keyword) => {
        this.selectOptions.keyword.value = keyword;
        this.performSearch();
      })
    );
  }

  override ngOnDestroy(): void {
    // Clean up search subject
    this.searchSubject.complete();
    
    // Clean up resize listener if exists
    if (this.resizeListener && this.isBrowser) {
      window.removeEventListener('resize', this.resizeListener);
    }
    
    super.ngOnDestroy();
  }



  // Performance optimized methods
  trackByComicId = (index: number, comic: Comic): number => {
    return comic?.id ?? index;
  };


  getGenreKeys() {
    return Object.keys(this.selectOptions.genres.value).filter(
      (key) => this.selectOptions.genres.value[key] > 0,
    );
  }
  private resetOptions(
    status: number,
    sort: number,
    year: number,
    genres: string,
    nogenres: string,
    keyword: string,
  ) {
    if (this.isOnInit) return;

    this.selectOptions.keyword.value = keyword;

    genres
      .split(',')
      .filter((x) => x != '')
      .forEach((id) => {
        this.selectOptions.genres.value[id] = 1;
        this.selectOptions.genres.label[id] = this.listGenres.find(
          (x) => x.id == parseInt(id),
        )?.title;
      });
    nogenres
      .split(',')
      .filter((x) => x != '')
      .forEach((id) => {
        this.selectOptions.genres.value[id] = 2;
        this.selectOptions.genres.label[id] = this.listGenres.find(
          (x) => x.id == parseInt(id),
        )?.title;
      });

    this.selectOptions.year.value = year;
    this.selectOptions.year.label = year.toString();

    this.dataView.status.forEach((filter) => {
      filter.selected = filter.value === status;
      if (filter.selected) {
        this.selectOptions.status.label = filter.label;
        this.selectOptions.status.value = filter.value;
      }
    });

    this.dataView.sorts.forEach((filter) => {
      filter.selected = filter.value === sort;
      if (filter.selected) {
        this.selectOptions.sorts.label = filter.label;
        this.selectOptions.sorts.value = filter.value;
      }
    });

    this.isOnInit = true;
  }

  private searchComics(
    page: number,
    sort: number,
    status: number,
    genres: string,
    nogenres: string,
    year: number,
    keyword: string,
  ) {
    this.listComics = [];
    this.isSearching = true;
    
    this.addSubscription(
      this.comicService
        .getAdvanceSearchComic(
          page,
          this.step,
          sort,
          status,
          genres,
          nogenres,
          year,
          keyword,
        )
        .pipe(this.takeUntilDestroy())
        .subscribe((res: any) => {
          this.totalpage = res.data.totalpage;
          this.listComics = res.data.comics;
          this.totalResult = ((this.totalpage - 1) * this.step + this.listComics.length);
          this.isLoading = false;
          this.isSearching = false;
          this.nPreview = this.listComics.length;
          
          // Update SEO with search results
          this.updateSearchSEO(keyword, this.totalResult, page);
          
          this.safeMarkForCheck();
        })
    );
  }

  OnFilterChange({ option, data }: { option: string; data: IOption }) {
    if (data.selected) return;
    this.dataView[option as keyof IFilters].forEach(
      (filter) => (filter.selected = false),
    );
    data.selected = true;

    this.selectOptions[option as keyof IFilters].label = data.label;
    this.selectOptions[option as keyof IFilters].value = data.value;
    this.selectOptions[option as keyof IFilters].isShow = false;
  }
  OnFilterChange1(key: string, value: any) {
    this.selectOptions[key].value = value;
    if (key === 'status') {
      this.selectOptions.status.label = this.dataView.status.find((x) => x.value === value)?.label;
    }
    else if (key === 'sorts') {
      this.selectOptions.sorts.label = this.dataView.sorts.find((x) => x.value === value)?.label;
    }

  }

  OnYearChange(year: number) {
    this.selectOptions.year.isShow = true;
    this.selectOptions.genres.isShow = false;
    this.selectOptions.year.value = this.selectOptions.year.value + year;
    this.selectOptions.year.label = year.toString();
  }
  OnGenresChange(genre: Genre) {
    const genreValue = this.selectOptions.genres.value[genre.id];
    this.selectOptions.genres.value[genre.id] = (genreValue + 1 || 1) % 3;
    this.selectOptions.genres.label[genre.id] = genre.title;
  }

  private updateGenres(): { genres: string; nogenres: string } {

    const genres: string[] = [];
    const nogenres: string[] = [];

    Object.entries(this.selectOptions.genres.value).forEach(
      ([key, value]) => {
        if (value === 1) genres.push(key);
        if (value === 2) nogenres.push(key);
      },
    );
    return {
      genres: genres.join(','),
      nogenres: nogenres.join(','),
    }
  }

  performSearch() {
    console.trace(this.selectOptions.genres.value);
    const { genres, nogenres } = this.updateGenres()
    this.queryParams =
    {
      ...this.queryParams,
      page: this.currentPage === 1 ? undefined : this.currentPage,
      status: this.selectOptions.status.value === ComicStatus.ALL ? undefined : this.selectOptions.status.value,
      sort: this.selectOptions.sorts.value === SortType.LastUpdate ? undefined : this.selectOptions.sorts.value,
      year: this.selectOptions.year.value === 0 ? undefined : this.selectOptions.year.value,
      genres: genres === '' ? undefined : genres,
      nogenres: nogenres === '' ? undefined : nogenres,
      keyword: this.selectOptions.keyword.value === '' ? undefined : this.selectOptions.keyword.value,

    }
    this.router.navigate([], {
      queryParams: this.queryParams,
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  removeFilterTag({ option, data }: { option: string; data: any }) {
    switch (option) {
      case 'status':
        this.selectOptions.status.value = -1;
        this.selectOptions.status.label = this.dataView.status[0].label;
        break;
      case 'sort':
        this.selectOptions.sorts.value = this.lastupdate;
        this.selectOptions.sorts.label = this.dataView.sorts[0].label;
        break;
      case 'year':
        this.selectOptions.year.value = 0;
        break;
      case 'genres':
        this.selectOptions.genres.value[data] = 0;
        break;
    }
  }

  getJoinedGenreNames(): string {
    return this.getGenreKeys()
      .map((key) => this.selectOptions.genres.label[key])
      .join(', ');
  }

  clearAllFilter() {
    this.dataView.status.map(
      (d: any, index: number) => (d.selected = index === 0),
    );
    this.dataView.sorts.map(
      (d: any, index: number) => (d.selected = index === 0),
    );

    this.selectOptions = {
      sorts: {
        value: SortType.LastUpdate,
        label: advancedFiltersOptions.sorts[0].label,
      },
      status: {
        value: ComicStatus.ALL,
        label: advancedFiltersOptions.status[0].label,
      },
      year: { value: 0, label: '', isShow: false },
      genres: { value: {}, label: {}, isShow: false },
      keyword: { value: '', label: '' },
    };
  }

  setupSEO() {
    // Initial setup for search page
    this.seoService.setSearchSEO();
  }

  updateSearchSEO(keyword: string, totalResults: number, page: number): void {
    // Update SEO based on search results
    this.seoService.setSearchSEO(keyword, totalResults, page);
  }
}
