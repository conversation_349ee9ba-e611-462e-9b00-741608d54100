import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { RouterLink } from '@angular/router';

export interface BreadcrumbLink {
  label: string | undefined;
  url: string | string[] | undefined;
}

@Component({
  selector: 'div[app-breadcrumb]',
  templateUrl: './breadcrumb.component.html',
  styleUrl: './breadcrumb.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [CommonModule, RouterLink],
})
export class BreadcrumbComponent {
  @Input() Links: BreadcrumbLink[] = [];

  /**
   * Style variant for breadcrumb background
   * - 'transparent': Transparent background with backdrop blur effects
   * - 'default': Default neutral background that adapts to theme
   */
  @Input() style: 'transparent' | 'default' = 'default';

  // TrackBy function for performance optimization
  trackByLink = (index: number, link: { label: string | undefined; url: string | string[] | undefined }): string => {
    return Array.isArray(link.url) ? link.url.join('/') : link.url || index.toString();
  };
}
