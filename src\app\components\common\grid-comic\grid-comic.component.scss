// Optimized Grid Comic Component - Tailwind @apply with responsive utilities

// Container
.grid-container {
  @apply mt-0 mb-3;
}

// Selection Controls
.selection-controls {
  @apply flex gap-2 justify-end mb-3;
}

.select-all-btn {
  @apply inline-flex items-center px-4 py-2.5 text-sm font-medium rounded-lg;
  @apply text-neutral-600 bg-neutral-200 hover:bg-neutral-300;
  @apply dark:bg-primary-100 dark:hover:bg-primary-200 dark:text-light-text;

  &.active {
    @apply bg-primary-100 text-white;
  }
}

.count-badge {
  @apply inline-flex items-center justify-center w-4 h-4 ml-2 text-xs font-semibold rounded-full;
  @apply bg-white text-neutral-600;

  &.active {
    @apply bg-primary-50 text-white;
  }
}

.delete-btn {
  @apply inline-flex items-center px-3 py-2.5 text-sm font-medium rounded-lg;
  @apply text-primary-100 focus:outline-none focus:ring-4 focus:ring-primary-50;

  &.disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}

.delete-icon {
  @apply w-3.5 h-3.5 mr-2;
}

// Header
.grid-header {
  @apply flex justify-between items-center gap-6 mb-3;
}

.title-section {
  @apply flex items-center px-1 rounded-sm ;
}



// Grid Type Switch
.grid-switch {
  @apply relative flex items-center bg-neutral-100 dark:bg-neutral-700 rounded-md p-0.5;
  @apply shadow-sm border border-neutral-200 dark:border-neutral-600;
}

.switch-track {
  @apply absolute inset-1 pointer-events-none;
}

.switch-thumb {
  @apply translate-x-full w-1/2 h-full bg-neutral-700 dark:bg-neutral-900 rounded-md shadow-sm;
  @apply transition-transform duration-300 ease-in-out;

  &.list-mode {
    @apply translate-x-0;
  }

  &.grid-mode {
    @apply translate-x-full;
  }
}

.switch-btn {
  @apply relative flex items-center justify-center p-2 min-w-10 text-sm font-medium;
  @apply text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white;
  @apply rounded-md border-none cursor-pointer z-10;
  @apply transition-colors duration-200;

  &.active {
    @apply text-white;
  }
}

.switch-icon {
  @apply w-5 h-5;
}


.list-grid {
  // @apply ;
}

.list-item-v2 {
  @apply relative lg:px-0;
}

.item-checkbox {
  @apply absolute z-10 m-2 w-4 h-4 rounded-lg border-2 border-white;
}

// Comic Popup
.comic-popup {
  @apply fixed z-20 mt-2 transition-all duration-100;
}
