<ng-container>
  <div
    app-breadcrumb
    class="z-10 my-2 md:container mx-auto w-full flex w-full"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: '<PERSON> dõ<PERSON>', url: 'theo-doi' }
    ]"
  ></div>
  <div class="text-center absolute top-1/2 left-1/2" *ngIf="isFirstLoading; else loadedContent">
    <div app-spinner [sizeSpinner]="'40'"></div>
  </div>
  <ng-template #loadedContent>
    <div id="comics" class="mt-6 w-full md:container mx-auto w-full">
      <div app-grid-comic [nPreview]="0" [listComics]="comics" [title]="'<PERSON> dõi'">
        <ng-template #toolTemplate *ngIf="isLogin">
          <a
            [routerLink]="['/dong-bo-truyen']"
            class="px-4 py-2 flex bg-primary-100 text-gray-50 hover:bg-primary-50 border rounded-lg items-center justify-center gap-1.5"
          >
            <svg class="size-5" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor">
              <path
                d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"
              ></path>
              <path
                d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"
              ></path>
            </svg>
            <span class="hidden lg:block"> Đồng bộ </span>
          </a>
        </ng-template>
        <ng-template #iconTemplate>
          <svg
            class="size-6"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <path d="M12 20l-7 -7a4 4 0 0 1 6.5 -6a.9 .9 0 0 0 1 0a4 4 0 0 1 6.5 6l-7 7" />
          </svg>
        </ng-template>
        <ng-template #actionTemplate let-comic="comic">
          <button
            (click)="onUnFollowClick([comic.id])"
            class="bg-red-500 hover:bg-primary-200 m-1.5 rounded-md absolute top-0 right-0 z-10 text-white"
          >
            <svg class="mx-1.5 my-1 size-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </button>
        </ng-template>
        <ng-template #emptyTemplate>
          <div
            class="mt-10 lg:mt-20 lg:min-h-1/2 flex justify-center flex-col items-center mx-auto"
          >
            <div app-empty class="w-1/3 lg:w-56"></div>
            <p *ngIf="isLogin" class="text-center">Bạn chưa theo dõi truyện nào</p>
            <p *ngIf="!isLogin" class="text-center">
              Vui lòng
              <a class="text-primary-200 font-bold" [routerLink]="['/auth/login']">đăng nhập </a> để
              xem danh sách truyện đã theo dõi
            </p>
          </div>
        </ng-template>
      </div>
      <nav
        aria-label="Pagination navigation"
        app-pagination
        [totalpage]="totalpage"
        [fragment]="'comics'"
        [currentPage]="currentPage"
      ></nav>
    </div>
  </ng-template>
</ng-container>
