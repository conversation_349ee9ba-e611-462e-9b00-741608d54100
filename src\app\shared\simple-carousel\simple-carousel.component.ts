import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ContentChild,
    ElementRef,
    EventEmitter,
    HostListener,
    Output,
    PLATFORM_ID,
    TemplateRef,
    TrackByFunction,
    ViewChild,
    ViewEncapsulation,
    computed,
    effect,
    inject,
    input,
    signal
} from '@angular/core';
import { BaseComponent } from '@components/common/base/component-base';
import { PreventClickOnDragDirective } from './prevent-click-on-drag.directive';

export interface SimpleCarouselResponsiveSetting {
    breakpoint: number; // max-width
    items: number;
}

@Component({
    selector: 'simple-carousel',
    standalone: true,
    imports: [CommonModule, PreventClickOnDragDirective],
    templateUrl: './simple-carousel.component.html',
    styleUrls: ['./simple-carousel.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: { class: 'relative overflow-x-hidden flex' }
})
export class SimpleCarouselComponent extends BaseComponent implements AfterViewInit {
    // Signal-based inputs (Angular >=17)
    listItems = input<any[]>([]); // Data array
    loop = input(true); // infinite loop
    autoplay = input(true);
    autoplayInterval = input(3000);
    pauseOnHover = input(true);
    speed = input(10000); // reserved for advanced easing/timing use
    showNav = input(true);
    showDots = input(false);
    snap = input(true); // snap to item after drag
    enableKeyboard = input(true);
    lazy = input(true);
    @Output() changed = new EventEmitter<number>();

    @ContentChild("itemTemplate") itemTemplate!: TemplateRef<any>; // For data-based usage
    @ViewChild('stage', { static: true }) stageRef!: ElementRef<HTMLElement>;

    // Core reactive state
    currentIndex = signal(0);
    translateX = signal(0);
    widthPx = signal(0);
    isTransitioning = signal(false);
    isDragging = signal(false);
    itemPerPage = signal(1); // visible items per page
    gapPx = signal(0); // gap between items in px (from CSS variable)
    displayItems = signal<any[]>([]); // real items only (clones handled in template)

    // Internal mutable helpers (not exposed to template)
    private autoplayTimer: any;
    private startX = 0;
    private currentDragX = 0;
    private needsReset = false;
    private velocity = 0;
    private lastTime = 0;
    private lastX = 0;

    // Derived computed signals
    totalItems = computed(() => this.displayItems().length);
    itemWidthPx = computed(() => {
        const per = this.itemPerPage();
        return per > 0 ? (this.widthPx()+this.gapPx()) / per: 0;
    });
    transform = computed(() => `translateX(${this.translateX()}px)`);
    trackByIndex: TrackByFunction<any> = (index: number, item: any) => index;

    constructor() {
        const platformId = inject(PLATFORM_ID);
        super(platformId);
        effect(() => {
            const items = this.listItems();
            this.displayItems.set(items.slice());
            if (this.isBrowser) {
                this.recalculate();
                this.setInitialPosition();
            }
        });
        // Autoplay effect
        effect(() => {
            if (!this.isBrowser) return;
            const canPlay = this.autoplay() && this.totalItems() > 1 && !this.isDragging();
            const interval = this.autoplayInterval();
            this.clearAutoplay();
            this.runOutsideAngular(() => {
            if (canPlay) {
                this.autoplayTimer = setInterval(() => {
                    if (!this.isTransitioning() && !this.isDragging()) this.next();
                }, interval);
            }
            });
        });
    }

    // ngOnChanges(changes: SimpleChanges): void {
    //     // Support external binding changes (non-signal parents)
    //     if (changes['listItems']) {
    //         this.updateDisplayItems();
    //         if (this.isBrowser) this.recalculate();
    //         this.setInitialPosition();
    //     }
    // }

    // ngAfterContentInit(): void {
    //     this.updateDisplayItems();
    // }

    ngOnInit(): void {
        this.registerEvents();
        // Sync list items reactively

    }
    private registerEvents(): void {
        
        const stage = this.stageRef?.nativeElement;
        if (!stage) return;
        // console.log('registerEvents');
        // stage.addEventListener('touchstart', this.onDragStart.bind(this), { passive: true });
        // stage.addEventListener('touchmove', this.onDragMove.bind(this), { passive: true });
    }

    private updateDisplayItems(): void {
        this.displayItems.set(this.listItems().slice());
    }

    private setInitialPosition(): void {
        if (this.loop() && this.displayItems().length > 1 && this.isBrowser) {
            const w = this.itemWidthPx();
            this.translateX.set(-w); // show first real slide after cloned last
        } else {
            this.translateX.set(0);
        }
    }

    ngAfterViewInit(): void {
        this.updateDisplayItems();
        if (this.isBrowser) this.recalculate();
        this.setInitialPosition();
    }

    private recalculate(): void {
        this.widthPx.set(this.stageRef?.nativeElement?.clientWidth || 0);
        const wrapper = this.stageRef?.nativeElement?.parentElement as HTMLElement;
        const cssItems = wrapper ? getComputedStyle(wrapper).getPropertyValue('--sc-items').trim() : '';
        let parsed = parseInt(cssItems, 10);
        if (!isNaN(parsed) && parsed > 0) this.itemPerPage.set(parsed);
         const gapStr = getComputedStyle(this.stageRef?.nativeElement?.parentElement as HTMLElement).getPropertyValue('--sc-gap').trim();
         parsed = parseInt(gapStr, 10);
        if (!isNaN(parsed) && parsed >= 0) this.gapPx.set(parsed);
    }


    next(): void { this.slideTo(this.currentIndex() + 1); }
    prev(): void { this.slideTo(this.currentIndex() - 1); }
    goTo(index: number): void { this.slideTo(index); }

    private slideTo(targetIndex: number): void {
        const total = this.totalItems();
        if (!total) return;
        const w = this.itemWidthPx();

        if (!this.loop() || total <= 1) {
            const clamped = Math.max(0, Math.min(total - this.itemPerPage(), targetIndex));
            this.currentIndex.set(clamped);
            this.translateX.set(-clamped * w);
            this.isTransitioning.set(true);
            this.changed.emit(this.currentIndex());
            return;
        }

        this.isTransitioning.set(true);
        if (targetIndex < 0) {
            this.translateX.set(-total * w); // cloned last
            this.currentIndex.set(total - 1);
            this.needsReset = true;
        } else if (targetIndex >= total - this.itemPerPage()) {
            this.translateX.set(-w); // cloned first
            this.currentIndex.set(0);
            this.needsReset = true;
        } else {
            this.translateX.set(-(targetIndex + 1) * w); // offset for cloned last
            this.currentIndex.set(targetIndex);
            this.needsReset = false;
        }
        this.changed.emit(this.currentIndex());
    }

    onTransitionEnd(): void {
        this.isTransitioning.set(false);
        if (this.needsReset && this.loop() && this.totalItems() > 1) {
            this.needsReset = false;
            const w = this.itemWidthPx();
            this.translateX.set(-(this.currentIndex() + 1) * w);
        }
    }

    private startAutoplay(): void { /* handled by effect */ }
    private clearAutoplay(): void { if (this.autoplayTimer) { clearInterval(this.autoplayTimer); this.autoplayTimer = null; } }

    @HostListener('window:resize') onResize() { 
        
        this.recalculate();
        this.slideTo(this.currentIndex());
    }

    @HostListener('mouseenter') onMouseEnter() { if (this.pauseOnHover()) this.clearAutoplay(); }
    @HostListener('mouseleave') onMouseLeave() { /* autoplay resumes via effect */ }

    onKeydown(ev: KeyboardEvent): void {
        if (!this.enableKeyboard()) return;
        const total = this.totalItems();
        if (!total) return;
        switch (ev.key) {
            case 'ArrowRight': ev.preventDefault(); this.next(); break;
            case 'ArrowLeft': ev.preventDefault(); this.prev(); break;
            case 'Home': ev.preventDefault(); this.goTo(0); break;
            case 'End': ev.preventDefault(); this.goTo(total - 1); break;
        }
    }

    onDragStart(ev: PointerEvent | TouchEvent) {
        
        this.isDragging.set(true);
        this.clearAutoplay();
        const point = this.getPoint(ev);
        this.startX = point.clientX;
        this.currentDragX = this.translateX();
        this.isTransitioning.set(true); // keep transition off while dragging via template condition
        this.lastTime = Date.now();
        this.velocity = 0;
        this.lastX = point.clientX;
    }
    onDragMove(ev: PointerEvent | TouchEvent) {
        ev.preventDefault();
        if (!this.isDragging() || this.totalItems() < this.itemPerPage()) return;
        const point = this.getPoint(ev);
        const now = Date.now();
        const dt = now - this.lastTime;
        if (dt > 50) {
            this.lastTime = now;
            this.velocity = (point.clientX - this.lastX) / dt;
            this.lastX = point.clientX;
        }
        const dx = point.clientX - this.startX;
        if (!this.loop()) {
            // Apply drag with some resistance at boundaries for non-loop mode
            const currentPos = this.currentDragX + dx;
            const maxTranslate = 0; // Can't go beyond first slide
            const minTranslate = -(this.totalItems() - 1) * this.itemWidthPx(); // Can't go beyond last slide

            if (currentPos > maxTranslate) {
                // Add resistance when dragging past first slide
                const overflow = currentPos - maxTranslate;
                this.translateX.set(maxTranslate + (overflow * 0.5));
            } else if (currentPos < minTranslate) {
                // Add resistance when dragging past last slide
                const overflow = minTranslate - currentPos;
                this.translateX.set(minTranslate - (overflow * 0.5));
            } else {
                this.translateX.set(currentPos);
            }
        } else {
            if (this.translateX() > -this.itemWidthPx()) {
                // Dragging past the cloned last slide at the beginning
                this.startX = point.clientX;
                this.currentDragX = this.translateX();
                this.translateX.set(-this.itemWidthPx() * (this.totalItems() - 3));
                return;
            }
            if (this.translateX() < -this.itemWidthPx() * (this.totalItems() - 2)) {
                // Dragging past the cloned first slide at the end
                this.startX = point.clientX;
                this.currentDragX = this.translateX();
                this.translateX.set(-this.itemWidthPx());
                return;
            }
            this.translateX.set(this.currentDragX + dx);

        }
    }
    onDragEnd(ev: PointerEvent | TouchEvent | MouseEvent) {
        if (!this.isDragging() || this.totalItems() < this.itemPerPage()) return;
        const now = Date.now();
        const dt = now - this.lastTime;
        this.lastTime = now;
        if (dt !== 0) this.velocity = (this.translateX() - this.currentDragX) / dt;
        this.isDragging.set(false);
        this.translateX.set(this.translateX() + this.velocity * this.itemPerPage() * 5);
        if (this.snap()) {
            const w = this.itemWidthPx();
            let nearestIndex = this.currentIndex();
            if (this.loop()) {
                const visualPosition = -this.translateX() / w;
                const roundedVisual = Math.round(visualPosition);
                if (roundedVisual <= 0) {
                    nearestIndex = this.totalItems() - 1;
                } else if (roundedVisual >= this.totalItems() + 1) {
                    nearestIndex = 0;
                } else {
                    nearestIndex = roundedVisual - 1;
                }
            } else {
                const logicalPosition = -this.translateX() / this.itemWidthPx();
                let decimalPart = logicalPosition - this.currentIndex();
                const sign = Math.sign(decimalPart);
                decimalPart = Math.abs(decimalPart);
                const n = Math.floor(decimalPart);
                const r = decimalPart - n;                
      
                nearestIndex = nearestIndex + sign * (n+r > 0.15 ? n + 1 : n);
                nearestIndex = Math.max(0, Math.min(this.totalItems() - this.itemPerPage(), nearestIndex));
            }
            this.slideTo(nearestIndex);
        }
    }

    private getPoint(ev: PointerEvent | TouchEvent | MouseEvent): { clientX: number, clientY: number } {
        if (ev instanceof TouchEvent) {
            const t = ev.touches[0] || ev.changedTouches[0];
            return { clientX: t.clientX, clientY: t.clientY };
        }
        return { clientX: ev.clientX, clientY: ev.clientY };
    }

}
