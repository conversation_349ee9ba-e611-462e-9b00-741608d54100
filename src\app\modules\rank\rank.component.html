<div
  app-breadcrumb
  class="z-10 mt-2 mb-5 md:container mx-auto w-full flex"
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Xế<PERSON> hạng', url: '/xep-hang' }
  ]"
></div>

<div id="content" class="md:container mx-auto w-full mt-5 ">
  <!-- Enhanced Header Section -->
  <div class="ranking-header">
    <div class="">
      <div class="ranking-title-section">
        <svg
          class="w-8 h-8 "
          fill="currentColor"
          viewBox="0 0 512 512"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
          <g id="SVGRepo_iconCarrier">
            <g>
              <g>
                <path
                  d="M464.98,146.286H47.02c-25.927,0-47.02,21.092-47.02,47.02v125.388c0,25.928,21.093,47.02,47.02,47.02H464.98 c25.928,0,47.02-21.092,47.02-47.02V193.306C512,167.378,490.908,146.286,464.98,146.286z M480.653,318.694 c0,8.643-7.03,15.673-15.673,15.673H47.02c-8.642,0-15.674-7.03-15.674-15.673V193.306c0-8.642,7.031-15.673,15.674-15.673H464.98 c8.643,0,15.673,7.031,15.673,15.673V318.694z"
                ></path>
              </g>
            </g>
            <g>
              <g>
                <polygon
                  points="315.626,238.151 274.426,232.165 256,194.831 237.574,232.165 196.374,238.151 226.188,267.212 219.149,308.245 256,288.871 292.851,308.245 285.812,267.212 "
                ></polygon>
              </g>
            </g>
            <g>
              <g>
                <polygon
                  points="176.72,238.151 135.52,232.165 117.094,194.831 98.67,232.165 57.469,238.151 87.282,267.212 80.244,308.245 117.094,288.871 153.946,308.245 146.907,267.212 "
                ></polygon>
              </g>
            </g>
            <g>
              <g>
                <polygon
                  points="454.531,238.151 413.33,232.165 394.905,194.831 376.48,232.165 335.28,238.151 365.093,267.212 358.054,308.245 394.905,288.871 431.756,308.245 424.718,267.212 "
                ></polygon>
              </g>
            </g>
          </g>
        </svg>
        <h1 class="ranking-title">Bảng Xếp Hạng Truyện Tranh</h1>
      </div>
      <p class="text-neutral-600 dark:text-neutral-400 mt-2">
        Khám phá những bộ truyện tranh được yêu thích nhất
      </p>
    </div>

    <!-- Enhanced Filter Controls -->
    <div class="ranking-filters">
      <div class="filter-group">
        <label class="filter-label">Sắp xếp theo</label>
        <div
          app-selection-2
          class="filter-select"
          [options]="dataView.sorts"
          (selectedValueChange)="onSortOptionChange($event)"
          [selectedValue]="selectOptions.sorts.value"
        ></div>
      </div>

      <div class="filter-group">
        <label class="filter-label">Trạng thái</label>
        <div
          app-selection-2
          class="filter-select"
          [options]="dataView.status"
          (selectedValueChange)="onStatusOptionChange($event)"
          [selectedValue]="selectOptions.status.value"
        ></div>
      </div>
    </div>
  </div>

  <!-- Enhanced Ranking List Section -->
  <div
    app-grid-comic
    id="listComic"
    [listComics]="listComics | slice : 0 : listComics.length"
  ></div>
  <nav
    aria-label="Pagination navigation"
    app-pagination
    [totalpage]="totalpage"
    [currentPage]="currentPage"
    [queryParams]="queryParams"
    [fragment]="'listComic'"
  ></nav>
</div>
