// Contact Page Container
.contact-container {
  @apply max-w-6xl mx-auto px-4 py-8 bg-white dark:bg-dark-bg text-neutral-900 dark:text-light-text;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
}

// Header Section
.contact-header {
  @apply text-center mb-12 py-16 bg-gradient-to-br from-sky-50 to-sky-100 dark:from-neutral-800 dark:to-neutral-900 rounded-2xl;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%);
    pointer-events: none;
  }
}

.contact-header-content {
  @apply relative z-10;
}

.contact-title {
  @apply text-4xl md:text-5xl font-bold mb-4 text-neutral-900 dark:text-light-text flex items-center justify-center gap-4;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-icon {
  @apply w-12 h-12 text-sky-600 dark:text-sky-400;
}

.contact-subtitle {
  @apply text-xl text-neutral-600 dark:text-neutral-300 mb-6 max-w-2xl mx-auto;
}

.contact-meta {
  @apply flex flex-wrap justify-center gap-6 text-sm text-neutral-500 dark:text-neutral-400;
}

.response-time,
.support-status {
  @apply px-4 py-2 bg-white dark:bg-neutral-800 rounded-full border border-neutral-200 dark:border-neutral-700;
}

// Quick Contact Section
.quick-contact-section {
  @apply mb-16;
}

.section-title {
  @apply text-3xl font-bold mb-8 text-center text-neutral-900 dark:text-light-text;
}

.quick-contact-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.contact-method-card {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-center hover:shadow-lg transition-all duration-300;
  
  &:hover {
    transform: translateY(-4px);
  }
}

.method-icon {
  @apply text-4xl mb-4 block;
}

.contact-method-card h3 {
  @apply text-lg font-semibold mb-2 text-neutral-900 dark:text-light-text;
}

.contact-method-card p {
  @apply text-neutral-600 dark:text-neutral-300 mb-4;
}

.contact-btn {
  @apply px-6 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 transition-colors duration-200 font-medium text-sm;
  
  &:focus {
    @apply ring-2 ring-sky-300 ring-opacity-50;
  }
}

// Contact Form Section
.contact-form-section {
  @apply mb-16;
}

.form-container {
  @apply max-w-4xl mx-auto;
}

.form-description {
  @apply text-center text-neutral-600 dark:text-neutral-300 mb-8;
}

.contact-form {
  @apply space-y-6;
}

.form-row {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-neutral-700 dark:text-neutral-300;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full px-4 py-3 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-light-text transition-all duration-200;
  
  &::placeholder {
    @apply text-neutral-400 dark:text-neutral-500;
  }
  
  &.error {
    @apply border-red-500 ring-2 ring-red-200;
  }
}

.form-textarea {
  @apply min-h-[120px];
}

.error-message {
  @apply text-red-500 text-sm;
}

.character-count {
  @apply text-xs text-neutral-500 dark:text-neutral-400 text-right;
}

// File Upload
.file-upload-area {
  @apply border-2 border-dashed border-neutral-300 dark:border-neutral-600 rounded-lg p-8 text-center cursor-pointer hover:border-sky-500 transition-colors duration-200;
  
  &.dragover {
    @apply border-sky-500 bg-sky-50 dark:bg-sky-900/20;
  }
}

.file-input {
  @apply hidden;
}

.upload-content {
  @apply space-y-2;
}

.upload-icon {
  @apply w-12 h-12 mx-auto text-neutral-400 dark:text-neutral-500;
}

.file-types {
  @apply text-xs text-neutral-500 dark:text-neutral-400;
}

.selected-files {
  @apply mt-4 space-y-2;
}

.file-item {
  @apply flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg;
}

.file-name {
  @apply font-medium text-neutral-900 dark:text-light-text;
}

.file-size {
  @apply text-sm text-neutral-500 dark:text-neutral-400;
}

.remove-file {
  @apply w-6 h-6 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors duration-200;
}

// Checkbox
.checkbox-label {
  @apply flex items-start gap-3 cursor-pointer;
}

.checkbox-input {
  @apply sr-only;
}

.checkbox-custom {
  @apply w-5 h-5 border-2 border-neutral-300 dark:border-neutral-600 rounded flex-shrink-0 mt-0.5;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 4px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: opacity 0.2s;
  }
}

.checkbox-input:checked + .checkbox-custom {
  @apply bg-sky-600 border-sky-600;
  
  &::after {
    opacity: 1;
  }
}

.checkbox-text {
  @apply text-sm text-neutral-700 dark:text-neutral-300;
}

.privacy-link {
  @apply text-sky-600 dark:text-sky-400 hover:underline;
}

// Submit Button
.form-actions {
  @apply text-center;
}

.submit-btn {
  @apply px-8 py-3 bg-sky-600 text-white rounded-lg hover:bg-sky-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium;
  
  &.loading {
    @apply cursor-wait;
  }
  
  &:focus {
    @apply ring-2 ring-sky-300 ring-opacity-50;
  }
}

.btn-icon {
  @apply w-5 h-5 inline-block mr-2;
}

.loading-content {
  @apply flex items-center justify-center gap-2;
}

.spinner {
  @apply w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}

// Contact Information Section
.contact-info-section {
  @apply mb-16;
}

.contact-info-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.info-card {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl;
}

.info-icon {
  @apply text-4xl mb-4 block;
}

.info-card h3 {
  @apply text-lg font-semibold mb-4 text-neutral-900 dark:text-light-text;
}

.info-details {
  @apply space-y-2;
}

.info-details p {
  @apply text-sm text-neutral-600 dark:text-neutral-300;

  strong {
    @apply text-neutral-900 dark:text-light-text;
  }

  a {
    @apply text-sky-600 dark:text-sky-400 hover:underline;
  }
}

// FAQ Section
.faq-section {
  @apply mb-16;
}

.faq-grid {
  @apply space-y-4;
}

.faq-item {
  @apply border border-neutral-200 dark:border-neutral-700 rounded-lg overflow-hidden;
}

.faq-question {
  @apply w-full p-4 text-left bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-colors duration-200 flex items-center justify-between;

  &.active {
    @apply bg-sky-50 dark:bg-sky-900/20;
  }
}

.faq-icon {
  @apply w-5 h-5 text-neutral-500 dark:text-neutral-400 transition-transform duration-200;

  &.rotated {
    transform: rotate(180deg);
  }
}

.faq-answer {
  @apply overflow-hidden transition-all duration-300;
  max-height: 0;

  &.open {
    max-height: 200px;
  }

  p {
    @apply p-4 bg-neutral-50 dark:bg-neutral-900 text-neutral-600 dark:text-neutral-300;
  }
}

// Success Modal
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white dark:bg-neutral-800 rounded-xl p-8 max-w-md mx-4 text-center;
}

.success-icon {
  @apply text-6xl mb-4;
}

.modal-content h3 {
  @apply text-xl font-semibold mb-4 text-neutral-900 dark:text-light-text;
}

.modal-content p {
  @apply text-neutral-600 dark:text-neutral-300 mb-6;
}

.modal-btn {
  @apply px-6 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 transition-colors duration-200;
}

// Responsive Design
@media (max-width: 768px) {
  .contact-title {
    @apply text-3xl flex-col gap-2;
  }

  .contact-meta {
    @apply flex-col gap-3;
  }

  .quick-contact-grid {
    @apply grid-cols-1 sm:grid-cols-2;
  }

  .form-row {
    @apply grid-cols-1;
  }

  .contact-info-grid {
    @apply grid-cols-1;
  }
}

// Animation Enhancements
.contact-container {
  animation: slideInUp 0.5s ease-out;
}

.contact-method-card,
.info-card {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Accessibility Improvements
.contact-btn:focus-visible,
.submit-btn:focus-visible,
.faq-question:focus-visible,
.privacy-link:focus-visible {
  @apply outline-none ring-2 ring-sky-500 ring-opacity-50;
}

// High Contrast Mode Support
@media (prefers-contrast: high) {
  .contact-method-card,
  .info-card,
  .faq-item {
    @apply border-2 border-black dark:border-white;
  }

  .form-input,
  .form-select,
  .form-textarea {
    @apply border-2 border-black dark:border-white;
  }
}

// Reduced Motion Support
@media (prefers-reduced-motion: reduce) {
  .contact-method-card,
  .info-card,
  .form-input,
  .form-select,
  .form-textarea,
  .contact-btn,
  .submit-btn,
  .faq-question,
  .faq-answer,
  .faq-icon {
    @apply transition-none;
  }

  .contact-container {
    animation: none;
  }

  .contact-method-card:hover,
  .info-card:hover {
    transform: none;
  }

  .faq-icon.rotated {
    transform: none;
  }
}

// Print Styles
@media print {
  .contact-container {
    @apply text-black bg-white;
  }

  .contact-header {
    @apply bg-white border border-neutral-300;
  }

  .contact-btn,
  .submit-btn {
    @apply bg-neutral-800;
  }

  .modal-overlay {
    @apply hidden;
  }
}

// Dark Mode Specific Styles
@media (prefers-color-scheme: dark) {
  .contact-header {
    @apply from-neutral-800 to-neutral-900;
  }

  .file-upload-area.dragover {
    @apply bg-sky-900/20;
  }
}

// Performance Optimizations for GPU Acceleration
.contact-method-card,
.info-card,
.modal-content,
.faq-question {
  transform: translateZ(0);
  backface-visibility: hidden;
}

// Loading State Enhancements
.form-loading {
  @apply opacity-50 pointer-events-none;
}

// Error State
.form-error {
  @apply border-red-500 bg-red-50 dark:bg-red-900/20;
}

// Success State
.form-success {
  @apply border-lime-500 bg-lime-50 dark:bg-lime-900/20;
}
