import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, Inject, inject, OnInit, PLATFORM_ID, signal, ViewEncapsulation } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { InViewportDirective } from '@directives/inviewport.directive';
import { LazyLoadDirective } from "@directives/lazyload.directive";
import { NumeralPipe } from '@pines/numeral.pipe';
import { IUserLite, LeaderboardService } from '@services/leaderboard.service';
import { LevelService } from '@services/level.service';
import { PopupService } from '@services/popup.service';
import { BaseComponent } from '../base/component-base';
import { SpinnerComponent } from '../spinner/spinner.component';

@Component({
  selector: 'div[app-top-users]',
  standalone: true,
  imports: [CommonModule, NumeralPipe, LazyLoadDirective, InViewportDirective, SpinnerComponent],
  templateUrl: './top-users.component.html',
  styleUrl: './top-users.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    class: 'w-full bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 shadow-sm overflow-hidden'
  }
})
export class TopUsersComponent extends BaseComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  // Signals for reactive state
  private readonly topUsersSignal = signal<IUserLite[]>([]);
  private readonly isLoadingSignal = signal<boolean>(false);

  // Computed properties
  readonly topUsers = computed(() => this.topUsersSignal());
  readonly isLoading = computed(() => this.isLoadingSignal());
  readonly hasUsers = computed(() => this.topUsers().length > 0);

  constructor(
    private popupService: PopupService,
    private leaderboardService: LeaderboardService,
    @Inject(PLATFORM_ID) protected override platformId: object,
    private levelService : LevelService
  ) {
    super(platformId);
  }

  ngOnInit(): void {
    this.runInBrowser(() => {
      // this.loadTopUsers();
    });
  }

  public loadTopUsers(): void {
    this.isLoadingSignal.set(true);
    this.leaderboardService.getTop5Users()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response) => {
          if (response.status === 1 && response.data) {
            this.topUsersSignal.set(response.data);
          } else {
          }
          this.isLoadingSignal.set(false);
        },
        error: (error) => {
          console.error('Error loading top users:', error);
          this.isLoadingSignal.set(false);
        }
      })

  }


  onUserClick(user: IUserLite): void {
      this.popupService.showUserInfo({ userID: user.id });
  }

  trackByUserId(index: number, user: IUserLite): number {
    return user.id;
  }

  getRankClass(rank: number): string {
    switch (rank) {
      case 1:
        return 'rank-gold';
      case 2:
        return 'rank-silver';
      case 3:
        return 'rank-bronze';
      default:
        return 'rank-default';
    }
  }

  getLevelTitle( experience: number, typeLevel: number): string {
    return this.levelService.getLevel(experience, typeLevel);
  }

  getUserName(user: IUserLite): string {
    return user.firstName + ' ' + (user.lastName ?? '');
  }


  getDefaultAvatar(): string {
    return '/default_avatar.jpg';
  }

  onImageError(event: any): void {
    event.target.src = this.getDefaultAvatar();
  }
}
