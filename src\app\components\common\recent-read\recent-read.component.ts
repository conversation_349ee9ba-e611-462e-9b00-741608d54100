import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Inject, OnInit, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { RouterLink } from '@angular/router';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { Comic } from '@schema';

import { LazyLoadDirective } from '@directives/lazyload.directive';
import { HistoryService } from '@services/history.service';
import { UrlService } from '@services/url.service';
import { BaseComponent } from '../base/component-base';
import { SpinnerComponent } from '../spinner/spinner.component';

@Component({
  selector: 'div[app-recent-read]',
  templateUrl: './recent-read.component.html',
  styleUrl: './recent-read.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [RouterLink, CommonModule, DateAgoPipe, SpinnerComponent, LazyLoadDirective],
  host: {
    class: 'bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 shadow-sm overflow-hidden'
  }
})
export class RecentReadComponent extends BaseComponent implements OnInit {
  public readonly urlService = inject(UrlService);
  listComics: Comic[] = [];
  nComics = 2;
  isLoading = true;
  constructor(
    private hisService: HistoryService,
    @Inject(PLATFORM_ID) platformId: object,

  ) { super(platformId); }

  ngOnInit(): void {
    this.isLoading = true;
    this.runInBrowser(() => {
      this.isLoading = false;
      this.listComics = this.hisService.GetHistorys().slice(0, 2) ?? [];
      this.listComics.forEach(c => {
        if (!c.coverImage?.startsWith("https://cdn1.anhtruyen.com/coverimg/")) {
          c.coverImage = "https://cdn1.anhtruyen.com/coverimg/" + c.coverImage;
        }
      })
      this.nComics = this.hisService.GetHistorys().length;
    });
  }

  // TrackBy function for performance optimization
  trackByComicId = (index: number, comic: Comic): number => {
    return comic.id;
  };
}
