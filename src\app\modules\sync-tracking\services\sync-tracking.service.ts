import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import {
  SyncConflict,
  SyncCredentials,
  SyncProgress,
  SyncResult,
  SyncSettings,
  SyncStage,
} from '../models/sync-tracking.models';
import { IServiceResponse } from '@schema';
import { UrlService } from '@services/url.service';

@Injectable({
  providedIn: 'root'
})
export class SyncTrackingService {
  private API_BASE = 'sync-tracking';

  // State management
  private progressSubject = new BehaviorSubject<SyncProgress>({
    stage: SyncStage.IDLE,
    progress: 0,
    message: 'Ready to sync'
  });

  private syncResultSubject = new BehaviorSubject<SyncResult | null>(null);

  // Progress polling
  private currentSessionId: string | null = null;
  private progressPollingInterval: any = null;

  // Public observables
  public progress$ = this.progressSubject.asObservable();
  public syncResult$ = this.syncResultSubject.asObservable();

  constructor(
    private http: HttpClient,
    private urlService: UrlService
  ) {
    this.API_BASE = this.urlService.buildUrl("/sync-tracking");
  }

  /**
   * Test API connection
   */
  testApiConnection(): Observable<boolean> {
    return this.http.get<{ success: boolean; message: string }>(`${this.API_BASE}/health`).pipe(
      map(response => response.success),
      catchError(() => of(false))
    );
  }

  /**
   * Cleanup method to be called when service is destroyed
   */
  ngOnDestroy(): void {
    this.stopProgressPolling();
  }

  // ===== MAIN SYNC METHODS =====

  /**
   * Start sync process with credentials
   */
  startSync(credentials: SyncCredentials): Observable<SyncResult> {
    this.updateProgress(SyncStage.CONNECTING, 0, 'Connecting to websites...');



    const payload = {
      credentials: this.encryptCredentials(credentials),
    };

    return this.http.post<IServiceResponse<{ sessionId: string; message: string }>>(`${this.API_BASE}/start`, payload).pipe(
      tap(response => {
        if (response.status === 200 && response.data?.sessionId) {
          this.currentSessionId = response.data?.sessionId;
          this.startProgressPolling();
        }
      }),
      map(response => {
        // Return a placeholder result - real result will come from progress polling
        return {
          success: response.status === 200,
          totalComics: 0,
          syncedComics: 0,
          errors: [],
          summary: {
            comics: 0,
            newComics: 0,
            updatedComics: 0,
            conflictComics: 0
          },
          duration: 0
        };
      }),
      catchError(error => {
        this.updateProgress(SyncStage.ERROR, 0, `Sync failed: ${error.message}`);
        return throwError(() => error);
      })
    );
  }





  /**
   * Test website credentials
   */
  testCredentials(site: 'nettruyen' | 'truyenqq', credentials: any): Observable<boolean> {

    const payload = {
      site,
      credentials: this.encryptCredentials(credentials)
    };

    return this.http.post<{ valid: boolean }>(`${this.API_BASE}/test-credentials`, payload).pipe(
      map(response => response.valid),
      catchError(() => throwError(() => new Error('Failed to test credentials')))
    );
  }

  // ===== PROGRESS POLLING METHODS =====

  /**
   * Start polling for sync progress
   */
  private startProgressPolling(): void {
    if (!this.currentSessionId) return;

    this.stopProgressPolling(); // Stop any existing polling

    this.progressPollingInterval = setInterval(() => {
      this.pollSyncProgress();
    }, 1000); // Poll every second
  }

  /**
   * Stop progress polling
   */
  private stopProgressPolling(): void {
    if (this.progressPollingInterval) {
      clearInterval(this.progressPollingInterval);
      this.progressPollingInterval = null;
    }
  }

  /**
   * Poll sync progress from server
   */
  private pollSyncProgress(): void {
    if (!this.currentSessionId) return;

    this.http.get<IServiceResponse<SyncProgress>>(`${this.API_BASE}/progress/${this.currentSessionId}`)
      .pipe(
        catchError(error => {
          console.error('Progress polling error:', error);
          this.stopProgressPolling();
          return throwError(() => error);
        })
      )
      .subscribe(response => {
        if (response.status === 200 && response.data) {
          this.handleProgressUpdate(response.data);
        }
      });
  }

  /**
   * Handle progress update from server
   */
  private handleProgressUpdate(serverProgress: SyncProgress): void {
    const stage = this.mapServerStageToClientStage(serverProgress.stage);

    this.updateProgress(
      stage,
      serverProgress.progress || 0,
      serverProgress.message || 'Processing...'
    );

    // Stop polling when sync is completed or failed
    if (stage === SyncStage.COMPLETED || stage === SyncStage.ERROR) {
      this.stopProgressPolling();

      if (stage === SyncStage.COMPLETED) {
        // Create final result from server data
        const result: SyncResult = {
          success: true,
          totalComics: serverProgress.totalComics || 0,
          syncedComics: serverProgress.processedComics || 0,
          errors: [],
          summary: {
            comics: 0,
            newComics: serverProgress.processedComics || 0,
            updatedComics: 0,
            conflictComics: 0
          },
          duration: this.calculateDuration(serverProgress.startTime, serverProgress.endTime)
        };
        this.syncResultSubject.next(result);
      }
    }
  }

  /**
   * Map server stage to client stage
   */
  private mapServerStageToClientStage(serverStage: string): SyncStage {
    const stageMap: { [key: string]: SyncStage } = {
      'idle': SyncStage.IDLE,
      'connecting': SyncStage.CONNECTING,
      'fetching': SyncStage.FETCHING, // Simplified mapping
      'comparing': SyncStage.COMPARING,
      'syncing': SyncStage.SYNCING,
      'completed': SyncStage.COMPLETED,
      'error': SyncStage.ERROR
    };

    return stageMap[serverStage] || SyncStage.IDLE;
  }

  /**
   * Calculate duration between start and end time
   */
  private calculateDuration(startTime?: Date, endTime?: Date): number {
    if (!startTime) return 0;

    const start = new Date(startTime).getTime();
    const end = endTime ? new Date(endTime).getTime() : Date.now();

    return Math.round((end - start) / 1000);
  }

  // ===== UTILITY METHODS =====

  /**
   * Update sync progress
   */
  private updateProgress(stage: SyncStage, progress: number, message: string, currentSite?: string): void {
    this.progressSubject.next({
      stage,
      progress,
      message,
      currentSite,
      currentAction: this.getActionForStage(stage)
    });
  }

  /**
   * Get action description for stage
   */
  private getActionForStage(stage: SyncStage): string {
    const actions = {
      [SyncStage.IDLE]: 'Waiting',
      [SyncStage.CONNECTING]: 'Connecting',
      [SyncStage.FETCHING]: 'Fetching NetTruyen data',
      [SyncStage.COMPARING]: 'Comparing data',
      [SyncStage.SYNCING]: 'Syncing changes',
      [SyncStage.COMPLETED]: 'Completed',
      [SyncStage.ERROR]: 'Error occurred'
    };
    return actions[stage] || 'Processing';
  }

  /**
   * Encrypt credentials for secure transmission
   */
  private encryptCredentials(credentials: any): string {
    // In production, use proper encryption
    return btoa(JSON.stringify(credentials));
  }

  /**
   * Handle HTTP errors
   */
  private handleError = (error: any) => {
    console.error('Sync service error:', error);
    return throwError(() => new Error(error.message || 'An error occurred'));
  };

  // ===== PUBLIC UTILITY METHODS =====

  /**
   * Cancel current sync process
   */
  cancelSync(): Observable<void> {
    if (!this.currentSessionId) {
      return throwError(() => new Error('No active sync session'));
    }

    return this.http.post<{ success: boolean; message: string }>(`${this.API_BASE}/cancel/${this.currentSessionId}`, {}).pipe(
      tap(() => {
        this.stopProgressPolling();
        this.updateProgress(SyncStage.IDLE, 0, 'Sync cancelled by user');
        this.currentSessionId = null;
      }),
      map(() => void 0),
      catchError(error => {
        console.error('Cancel sync error:', error);
        // Force reset even if cancel request fails
        this.resetSync();
        return throwError(() => error);
      })
    );
  }

  /**
   * Reset sync state
   */
  resetSync(): void {
    this.stopProgressPolling();
    this.currentSessionId = null;

    this.progressSubject.next({
      stage: SyncStage.IDLE,
      progress: 0,
      message: 'Ready to sync'
    });
    this.syncResultSubject.next(null);
  }

  /**
   * Get current progress
   */
  getCurrentProgress(): SyncProgress {
    return this.progressSubject.value;
  }

  /**
   * Check if sync is in progress
   */
  isSyncInProgress(): boolean {
    const stage = this.progressSubject.value.stage;
    return stage !== SyncStage.IDLE && stage !== SyncStage.COMPLETED && stage !== SyncStage.ERROR;
  }

  // ===== MOCK SIMULATION METHODS =====

}
