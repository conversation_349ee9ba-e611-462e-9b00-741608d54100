import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic, ComicList, IServiceResponse } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import { Subscription } from 'rxjs';

@Component({
    selector: 'main[app-comic-hot]',
    templateUrl: './comic-hot.component.html',
    styleUrl: './comic-hot.component.scss',
    standalone: false
    , changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ComicHotComponent extends OptimizedBaseComponent implements OnInit {
  listComics?: Comic[] | null;
  totalpage!: number;
  page = 1;
  subscription!: Subscription;
  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    override cd: ChangeDetectorRef,
    private urlService: UrlService,
    private seoService: SeoService,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.route.queryParams.pipe(this.takeUntilDestroy()).subscribe((params: any) => {
      this.page = Number(params['page']) || 1;
      this.fetchHotComics();
    });
  }

  fetchHotComics() {
    this.listComics = [];
    this.comicService.getHotComics(this.page).pipe(this.takeUntilDestroy()).subscribe((res: any) => {
      if (res.data) {
        this.listComics = res.data.comics;
        this.totalpage = res.data.totalpage;
        this.cd.detectChanges();
        this.setupSeo();

      }
    });
  }


  setupSeo() {
    // Use the new comprehensive SEO method
    this.seoService.setHotComicsSEO(this.listComics || [],this.page);
  }
}
