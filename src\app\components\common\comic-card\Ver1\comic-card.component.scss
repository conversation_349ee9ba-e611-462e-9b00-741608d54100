// Hot Tag Styles



.top-left,
.top-right {
  @apply m-1 transform rounded-md absolute font-bold z-10 text-center top-0 text-white;
}

.star-icon {
  @apply text-yellow-400 mr-1;
}

.top-left {
  @apply min-w-7 text-white bg-black/60 left-0 px-1 py-0 xs:px-1.5 xs:py-0.5 text-[0.7rem] hidden items-center justify-center @xs/main:flex;
}

.top-right {
  @apply bg-primary-200/80 flex items-center justify-center  right-0;
}

.hot-tag-text {
  @apply  px-1 py-0 xs:px-1.5 xs:py-0.5 text-[0.7rem];
}

.hot-tag-animated {
  @apply animate-ping absolute p-1 text-[0.6rem];
}

// Card Container
.container-card-v1 {
  @apply aspect-[5/7] relative transition-transform duration-300 hover:-translate-y-2;
}

// Comic Link and Image
.comic-link {
  @apply flex h-full w-full;
}

.comic-card-image {
  @apply object-cover aspect-[5/7] w-full h-full;
}

// Footer Overlay
.footer-card-v1 {
  @apply w-full bg-gradient-to-t from-black to-black/20 absolute bottom-0;
}

// Comic Info Section
.comic-info {
  @apply flex text-white text-xs p-1 w-full flex-col;
}

.card-comic-title {
  @apply font-semibold line-clamp-2 text-sm;
}

.comic-status-view {
  @apply flex justify-between w-full;
}


.status-text {
  @apply font-light text-xs line-clamp-1 dark:text-neutral-400;
}

// View Count Section
.card-view-count {
  @apply flex gap-1 items-center text-neutral-200 fill-slate-200;
}

.view-count-text {
  @apply font-normal text-[0.7rem] text-center uppercase;
}

// Card Footer
.card-footer {
  @apply gap-2 px-1 py-2 flex justify-between text-xs h-full dark:bg-neutral-800;

  &:hover {
    @apply dark:hover:bg-zinc-700 bg-gradient-to-t from-primary-50/40 to-white/80 dark:from-dark-bg dark:to-dark-bg/50;
  }
}

.chapter-title {
  @apply font-semibold text-nowrap;
}

.chapter-update {
  @apply text-end text-xs inline text-neutral-700 dark:text-neutral-400 line-clamp-1 text-nowrap;
}

// Event Footer
.text-footer {
  @apply p-1 cursor-pointer font-bold flex-row text-center justify-between items-center text-[0.75rem] bg-slate-400 dark:bg-neutral-600;

  &:hover {
    @apply bg-red-500;
  }
}

// Placeholder Styles
.placeholder-image {
  @apply aspect-[5/7] animate-pulse bg-neutral-300 dark:bg-neutral-700 relative w-full ;
}

.placeholder-content {
  @apply flex justify-center items-center w-full h-full top-0 absolute;
}

.view-count-icon {
  @apply h-10 w-10 text-neutral-200 dark:text-neutral-600;
}


// Status Section - Enhanced Colors
.status-container {
  @apply text-sm text-center flex items-center;
}

.status-ongoing {
  @apply flex gap-2 items-center;
}

.status-indicator.ongoing {
  @apply  size-1.5 rounded-full bg-sky-400 opacity-75 ml-1 mr-2;
}