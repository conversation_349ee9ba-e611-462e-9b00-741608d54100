import { isPlatformBrowser } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Inject,
  NgZone,
  PLATFORM_ID
} from '@angular/core';

@Component({
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export abstract class BaseComponent  {
  protected readonly ngZone = inject(NgZone);
  protected isBrowser: boolean;
  protected isServer: boolean;
  

  constructor(
    @Inject(PLATFORM_ID) protected platformId: object,
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.isServer = !this.isBrowser;
  }

  protected async loadHeavyComponent() { }

  protected runInBrowser(callback: () => void): void {
    if (this.isBrowser) {
      callback();
    }
  }

  protected runInServer(callback: () => void): void {
    if (this.isServer) {
      callback();
    }
  }

  protected runOutsideAngular(callback: () => void): void {
    this.ngZone.runOutsideAngular(() => {
      callback();
    });
  }

}
