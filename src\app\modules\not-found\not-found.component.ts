import { Location } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional, PLATFORM_ID, RESPONSE_INIT } from '@angular/core';
import { Router } from '@angular/router';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';

@Component({
    selector: 'main[app-not-found]',
    templateUrl: './not-found.component.html',
    styleUrl: './not-found.component.scss',
    standalone: false,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotFoundComponent implements OnInit {
  searchQuery: string = '';

  constructor(
    @Inject(PLATFORM_ID) private platformId: object,
    @Optional() @Inject(RESPONSE_INIT) private response: ResponseInit,
    private seoService: SeoService,
    private router: Router,
    private location: Location,
    private urlService: UrlService,
  ) {
    this.setupSEO();
  }

  ngOnInit() {
    if(this.response) {
      this.response.status = 404;
    }
  }

  /**
   * Setup SEO meta tags for 404 page
   */
  private setupSEO(): void {
    // Use the new comprehensive SEO method
    this.seoService.setNotFoundSEO();
  }

  /**
   * Navigate back to previous page
   */
  goBack(): void {
    this.location.back();
  }

  /**
   * Perform search with current query
   */
  performSearch(): void {
    if (this.searchQuery.trim()) {
      this.router.navigate(['/tim-kiem'], {
        queryParams: { q: this.searchQuery.trim() }
      });
    }
  }
}
