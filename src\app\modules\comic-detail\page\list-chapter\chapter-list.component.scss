
// .txt-rank {
//     position: relative;
//     font-weight: bold;
//     color: white;
//     left: 13.5px;
//     text-align: center;
//     height: 30px;
//     width: 30px;
//     z-index: 100; // or any other value you prefer
//   }
  
/* filepath: /d:/Project/comic-client/src/app/modules/comic-detail/page/list-chapter/chapter-list.component.scss */
.chapter-panel {
  @apply flex flex-col gap-2 lg:flex-row justify-between;
}

.chapter-title {
  @apply  font-semibold uppercase mt-3 flex items-center ;
}

.chapter-title-text {
  @apply ml-2 font-extrabold text-xl text-gray-700 dark:text-light-text;
}

.chapter-controls {
  @apply flex justify-between items-center;
}

.chapter-search-container {
  @apply relative mr-2;
}

.chapter-search-icon {
  @apply absolute inset-y-0 start-0 flex items-center px-3 pointer-events-none;
}

.chapter-search-svg {
  @apply w-3 h-3 text-neutral-400 dark:text-neutral-400;
}

.chapter-search-input {
  @apply block w-48 pt-1 px-10 text-sm text-neutral-900 border-b border-solid border-[#ccc] dark:bg-dark-bg outline-none focus:ring-primary-100 focus:border-primary-100 dark:border-neutral-600 dark:placeholder-neutral-400 dark:text-light-text dark:focus:ring-primary-100 dark:focus:border-primary-100;
}

.chapter-selection {
  @apply w-32 ml-2;
}

.chapter-not-found {
  @apply font-light bg-neutral-100 dark:bg-neutral-800 h-20 rounded-lg my-3 p-3 text-neutral-600 dark:text-neutral-200;
}

.chapter-list-container {
  @apply flex w-full mt-4 relative ;
}

.chapter-list-overlay {
  @apply absolute top-0 right-0 -z-50 left-0;
}

.chapter-list {
  @apply grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3 relative mr-2;
}

.chapter-item {
  @apply  mr-2 mb-2 h-12 flex items-center justify-between col-span-2 bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-700 dark:hover:bg-neutral-500 text-sm rounded-lg px-2 py-1 cursor-pointer select-none;
}

.chapter-item-read {
  @apply bg-neutral-200 dark:bg-neutral-800;
}

.chapter-item-content {
  @apply p-1;
}


.chapter-item-date {
  @apply ml-auto p-1;
}

.chapter-item-date-text {
  @apply text-neutral-500 dark:text-neutral-300 text-xs;
}

.chapter-loading {
  @apply absolute top-0 right-0 w-full h-full min-h-48 flex justify-center items-center;
}