// Modern Comic-themed Authentication Styles
.auth-container {
  @apply min-h-screen relative overflow-hidden ;
   background: linear-gradient(135deg, #f1a863ca 0%, #f605053e 100%);
}

// Background Elements
.auth-background {
  @apply absolute inset-0 pointer-events-none;
}

.comic-bubbles {
  @apply absolute inset-0;
}

.bubble {
  @apply absolute text-4xl opacity-20 animate-bounce;
  animation-duration: 3s;
  animation-iteration-count: infinite;

  &.bubble-1 {
    @apply top-20 left-20;
    animation-delay: 0s;
  }

  &.bubble-2 {
    @apply top-40 right-32;
    animation-delay: 1s;
  }

  &.bubble-3 {
    @apply bottom-40 left-32;
    animation-delay: 2s;
  }

  &.bubble-4 {
    @apply bottom-20 right-20;
    animation-delay: 0.5s;
  }
}

// Main Content Layout
.auth-content {
  @apply relative z-10 min-h-screen flex items-center justify-center p-4;
  @apply lg:grid lg:grid-cols-2 lg:gap-8 lg:max-w-7xl lg:mx-auto;
}

// Left Side - Branding
.auth-branding {
  @apply hidden lg:flex lg:items-center lg:justify-center;
}

.brand-content {
  @apply text-white text-center max-w-md;
}

.brand-logo {
  @apply mb-8;
}

.logo-icon {
  @apply text-6xl mb-2 size-28;
}

.brand-title {
  @apply text-4xl font-bold mb-2;
}

.brand-accent {
  @apply text-primary-100;
}

.brand-subtitle {
  @apply text-xl opacity-90 mb-8;
}

.brand-features {
  @apply space-y-4;
}

.feature-item {
  @apply flex items-center gap-3 text-lg;
}

.feature-icon {
  @apply text-2xl;
}

.feature-text {
  @apply opacity-90;
}

// Right Side - Form Container
.auth-form-container {
  @apply w-full max-w-md mx-auto;
}

.auth-form-card {
  @apply bg-white dark:bg-neutral-900 rounded-2xl shadow-2xl p-8;
  @apply border border-neutral-200 dark:border-neutral-700;
  backdrop-filter: blur(10px);
}

// Form Header
.form-header {
  @apply text-center mb-8;
}

.form-title {
  @apply text-3xl font-bold text-neutral-900 dark:text-light-text mb-3;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-subtitle {
  @apply text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed;
}

// Form Styles
.auth-form {
  @apply space-y-6;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply flex items-center gap-2 text-sm font-semibold text-neutral-700 dark:text-neutral-300;
}

.label-icon {
  @apply text-lg;
}

.input-wrapper {
  @apply relative;
}

.form-input {
  @apply w-full px-4 py-3 pr-12 border border-neutral-300 dark:border-neutral-600;
  @apply rounded-xl bg-neutral-50 dark:bg-neutral-800;
  @apply text-neutral-900 dark:text-light-text placeholder-neutral-500 dark:placeholder-neutral-400;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100 focus:border-transparent;
  @apply transition-all duration-200;

  &:focus {
    @apply bg-white dark:bg-neutral-700 shadow-lg;
    transform: translateY(-1px);
  }
}

.input-icon {
  @apply absolute right-4 top-1/2 transform -translate-y-1/2;
  @apply text-neutral-400 dark:text-neutral-500;
}

.password-toggle {
  @apply absolute right-4 top-1/2 transform -translate-y-1/2;
  @apply text-neutral-400 hover:text-neutral-600 dark:text-neutral-500 dark:hover:text-neutral-300;
  @apply transition-colors duration-200;
}

.error-message {
  @apply text-red-500 text-xs mt-1 flex items-center gap-1;

  &::before {
    content: '⚠️';
    @apply text-sm;
  }
}

// Form Options
.form-options {
  @apply flex items-center justify-between;
}

.remember-me {
  @apply flex items-center gap-2;
}

.form-checkbox {
  @apply w-4 h-4 text-primary-100 bg-neutral-100 border-neutral-300 rounded;
  @apply focus:ring-primary-100 dark:focus:ring-primary-100 dark:ring-offset-neutral-800;
  @apply focus:ring-2 dark:bg-neutral-700 dark:border-neutral-600;
}

.checkbox-label {
  @apply text-sm text-neutral-600 dark:text-neutral-400 cursor-pointer;
}

.forgot-password-link {
  @apply text-sm text-primary-100 hover:text-primary-200 font-medium;
  @apply transition-colors duration-200;
}

// Submit Button
.submit-button {
  @apply w-full bg-gradient-to-r from-primary-100 to-primary-200;
  @apply text-white font-semibold py-3 px-6 rounded-xl;
  @apply hover:from-primary-200 hover:to-primary-100;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100 focus:ring-offset-2;
  @apply transition-all duration-200 transform hover:scale-105;
  @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
}

.button-content {
  @apply flex items-center justify-center gap-2;
}

.button-icon {
  @apply text-lg;
}

.button-text {
  @apply font-semibold;
}

// Divider
.form-divider {
  @apply flex items-center gap-4 my-6;
}

.divider-line {
  @apply flex-1 h-px bg-neutral-300 dark:bg-neutral-600;
}

.divider-text {
  @apply text-sm text-neutral-500 dark:text-neutral-400 font-medium;
}

// Social Login
.social-login {
  @apply flex justify-center mb-6;
}

.google-button {
  @apply w-full max-w-xs;
}

// Auth Switch
.auth-switch {
  @apply text-center;
}

.switch-text {
  @apply text-sm text-neutral-600 dark:text-neutral-400;
}

.switch-link {
  @apply text-primary-100 hover:text-primary-200 font-semibold;
  @apply transition-colors duration-200;
}

// Responsive Design
@media (max-width: 1024px) {
  .auth-content {
    @apply block;
  }

  .auth-branding {
    @apply hidden;
  }

  .auth-form-container {
    @apply max-w-sm;
  }
}

@media (max-width: 640px) {
  .auth-form-card {
    @apply p-6;
  }

  .form-title {
    @apply text-2xl;
  }

  .brand-title {
    @apply text-3xl;
  }

  .bubble {
    @apply text-2xl;
  }
}