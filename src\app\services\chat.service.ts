import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IConversation, IMessage, IMessagePage, ISendMessage, IServiceResponse } from '@schema';
import { EventSourcePolyfill } from 'event-source-polyfill';
import { Observable } from 'rxjs';
import { AccountService } from './account.service';
import { UrlService } from './url.service';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private readonly baseUrl = `${this.UrlService.apiUrl}/chat`;

  constructor(private auth: AccountService, private urlService: UrlService, private http: HttpClient, private UrlService: UrlService) { }

  // Create or get existing conversation

  // Get user's conversations
  getMyConversations(): Observable<IServiceResponse<IConversation[]>> {
    return this.http.get<IServiceResponse<IConversation[]>>(`${this.baseUrl}/conversations`);
  }

  // Get specific conversation
  getConversation(conversationId: string): Observable<IServiceResponse<IConversation>> {
    return this.http.get<IServiceResponse<IConversation>>(`${this.baseUrl}/conversation/${conversationId}`);
  }

  // Send message
  sendMessage(request: ISendMessage): Observable<IServiceResponse<IMessage>> {
    return this.http.post<IServiceResponse<IMessage>>(`${this.baseUrl}/message`, request);
  }

  // Get messages from conversation
  getMessages(conversationId: string, page: number = 1, pageSize: number = 50): Observable<IServiceResponse<IMessagePage>> {
    return this.http.get<IServiceResponse<IMessagePage>>(`${this.baseUrl}/messages/${conversationId}?page=${page}&pageSize=${pageSize}`);
  }

  chatWithBot(userMessageId: string): EventSourcePolyfill | undefined {
    if (!this.auth.isAuthenticated()) return;
    const url = `${this.urlService.apiUrl}/chat/chatbot/${userMessageId}`;
    return new EventSourcePolyfill(url, {
      headers: {
        'Authorization': `Bearer ${this.auth.getAuthorizationToken()}`
      }
    }); // Tạo EventSource

  }

}
