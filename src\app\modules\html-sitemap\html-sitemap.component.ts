import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { SeoService } from '@services/seo.service';

@Component({
  selector: 'main[app-html-sitemap]',
  templateUrl: './html-sitemap.component.html',
  styleUrls: ['./html-sitemap.component.scss'],
  standalone: true,
  imports: [
    RouterModule,
    CommonModule,
    BreadcrumbComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class HtmlSitemapComponent implements OnInit {

  constructor(private seo: SeoService) {}
  ngOnInit(): void {
    this.seo.setHtmlSitemapSEO();
  }
}
