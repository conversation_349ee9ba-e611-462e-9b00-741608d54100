<div
  #VirtualScroll
  class="w-full overflow-y-auto overflow-x-hidden relative scrollbar-style-1 min-h-0"
>
  <div #VirtualScrollSpace class="absolute top-0 w-1 -z-50 left-0"></div>
  <div #VirtualScrollContent   class="grid" [ngClass]="'grid-cols-'+gridSize">
    <ng-container *ngFor="let item of preLoadItems; let i = index; trackBy: trackById">
        <ng-template
            [ngTemplateOutlet]="itemTemplateRef"
            [ngTemplateOutletContext]="{ $implicit: item, item : item, index: i }"
        >
        </ng-template>
    </ng-container>
  </div>
</div>
<ng-content />

