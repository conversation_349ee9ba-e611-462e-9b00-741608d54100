<div class="hidden sm:flex mt-3 mb-2 flex-row justify-between gap-6 rounded-t">
  <div class="min-w-32 flex items-center">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      class="h-6 w-6"
      viewBox="0 0 32 32"
      id="icon"
    >
      <defs>
        <style>
          .cls-1 {
            fill: none;
          }
        </style>
      </defs>
      <title>Truyện đang thịnh hành</title>
      <path
        d="M16,2a9,9,0,0,0-6,15.69V30l6-4,6,4V17.69A9,9,0,0,0,16,2Zm4,24.26-2.89-1.92L16,23.6l-1.11.74L12,26.26V19.05a8.88,8.88,0,0,0,8,0ZM20.89,16A7,7,0,1,1,23,11,7,7,0,0,1,20.89,16Z"
      />
    </svg>
    <h2 class="block-title"><PERSON><PERSON><PERSON><PERSON><PERSON> đ<PERSON> thịnh hành</h2>
  </div>
</div>

<simple-carousel
  class="sm:mt-2 h-56 sm:mx-0 simple-carousel-wrapper"
  [listItems]="carouselComics"
  [loop]="false"
  [autoplay]="true"
  [snap]="true"
  [autoplayInterval]="4000"
  (changed)="onCarouselChanged($event)"
>
  <ng-template #itemTemplate let-comic let-i="index">
    <!-- Clickable Link Overlay -->

    <a
      draggable="false"
      [routerLink]="urlService.getComicDetailUrl(comic)"
      [attr.aria-label]="'Đọc truyện ' + comic.title"
      class="group relative p-4 w-full h-full xl:rounded-xl text-white dark:text-light-text flex overflow-hidden"
    >
      <!-- Blurred Background Image -->
      <img
        [loading]="i < 1 ? 'eager' : 'lazy'"
        draggable="false"
        [src]="comic.coverImage"
        [alt]="'Bìa truyện ' + comic.title"
        class="grayscale absolute blur-[2px] inset-0 w-full h-full object-cover "
        fetchpriority="high"
      />

      <!-- Dark Overlay -->
      <div class="absolute inset-0 bg-black/50"></div>

      <!-- Small Thumbnail in Top Left -->

      <div class="flex-grow flex flex-col gap-2 z-10 pr-4">
        <h3 class="text-xl sm:text-2xl font-bold line-clamp-2 drop-shadow-lg leading-tight">
          {{ comic.title }}
        </h3>

        <!-- Genres -->
        <div class="flex flex-wrap gap-1" *ngIf="comic.genres && comic.genres.length">
          <span
            *ngFor="let genre of comic.genres.slice(0, 3)"
            class="text-xs bg-primary-100/50 backdrop-blur-sm px-2 py-0.5 rounded-full font-medium"
          >
            {{ genre.title || genre.name }}
          </span>
        </div>

        <p class="z-50 text-sm line-clamp-2 mt-6">
          {{ comic.description | fillDescription : comic.id : comic.title : comic.url : false }}
        </p>

        <!-- Meta Info -->
        <div class="flex items-center justify-between text-sm text-gray-200">
          <div class="flex items-center space-x-2">
            <!-- Rating and Status Row -->
            <div class="flex justify-between items-center">
              <!-- Rating Badge -->
              <div
                class="bg-black/50 backdrop-blur-sm px-2 py-1 rounded-full flex"
                *ngIf="comic.rating"
              >
                <svg class="w-4 h-4 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                  />
                </svg>
                <span class="text-xs font-bold">{{ comic.rating }}</span>
              </div>
            </div>
            <!-- Author -->
            <!-- <span *ngIf="comic.author" class="bg-black/50 backdrop-blur-sm px-2 py-1 rounded-full">
                {{ comic.author }}
              </span> -->
            <!-- Views -->
            <span
              class="bg-black/50 backdrop-blur-sm text-xs px-2 py-1 rounded-full flex items-center"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              {{ comic.viewCount | numeral }}
            </span>
            <span class="text-xs bg-black/50 backdrop-blur-sm px-2 py-1 rounded-full flex">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                />
              </svg>
              {{ comic.numChapter }}
              <span class="ml-1 hidden md:inline"> chương</span>
            </span>
          </div>
        </div>
      </div>
      <div class="flex-shrink-0 z-20">
        <img
          [loading]="i < 1 ? 'eager' : 'lazy'"
          draggable="false"
          [src]="comic.coverImage"
          [alt]="'Thumbnail ' + comic.title"
          class="w-32 h-48 object-cover rounded-lg border-2 border-white/80 shadow-lg"
          
        />
      </div>
      <!-- Content Container -->
    </a>
  </ng-template>
</simple-carousel>

<div app-anouncement></div>

<!-- <div app-carousel-landing class="carousel-landing"></div> -->

<a
  target="_blank"
  href="https://saytruyenhot.com"
  class="flex flex-col sm:flex-row mt-2 w-full aspect-[13/1] sm:aspect-[26/1]"
>
  <img src="saytruyenmoi.webp" class="object-contain sm:w-1/2" alt="saytruyenhot.com" />
  <img
    src="saytruyenmoi.webp"
    class="object-contain hidden sm:block sm:w-1/2"
    alt="saytruyenhot.com"
  />
</a>

<div class="mt-4 grid grid-cols-1 xl:grid-cols-4 gap-2 lg:gap-4">
  <div id="comics" class="xl:col-span-3 row-span-3">
    <div app-grid-comic [listComics]="listComics" [nPreview]="20" [title]="'Mới cập nhật'">
      <ng-template #iconTemplate>
        <svg
          class="size-6"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" />
          <path d="M19 18a3.5 3.5 0 0 0 0 -7h-1a5 4.5 0 0 0 -11 -2a4.6 4.4 0 0 0 -2.1 8.4" />
          <line x1="12" y1="13" x2="12" y2="22" />
          <polyline points="9 19 12 22 15 19" />
        </svg>
      </ng-template>
    </div>
    <nav
      app-pagination
      [fragment]="'comics'"
      [currentPage]="currentPage"
      [totalpage]="totalpage"
      aria-label="Pagination navigation"
    ></nav>
  </div>
  <div app-recent-read class="row-start-1 xl:row-start-auto"></div>
  <div class="flex flex-col gap-4">
    <div app-top-list></div>
    <div *ngIf="!isServer" app-top-users class="mb-4 xl:mb-0"></div>
  </div>
</div>

<div class="mt-6 p-4">
  <!-- Header -->
  <div class="text-center mb-8">
    <h1 class="text-2xl md:text-3xl font-bold mb-4">
      MeTruyenMoi - Website Đọc Truyện Tranh Online Hàng Đầu Việt Nam
    </h1>
    <p class="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
      Khám phá thế giới truyện tranh đầy màu sắc với kho tàng khổng lồ gồm manga Nhật Bản, manhwa
      Hàn Quốc, manhua Trung Quốc hoàn toàn miễn phí.
    </p>
  </div>

  <!-- Content Preview with Height Animation -->
  <div class="relative">
    <div class="seo-content-container" [class.expanded]="isContentExpanded">
      <div class="prose prose-gray dark:prose-invert max-w-none">
        <div class="space-y-6 text-gray-700 dark:text-gray-300">
          <!-- Section 1 -->
          <div>
            <h2 class="text-xl font-semibold mb-3 flex items-center">
              MeTruyenMoi - Điểm Đến Tin Cậy Của Mọi Tín Đồ Truyện Tranh
            </h2>
            <p class="leading-relaxed">
              <strong>MeTruyenMoi</strong> tự hào là một trong những nền tảng đọc truyện tranh
              online hàng đầu tại Việt Nam. Với cam kết mang đến trải nghiệm đọc truyện tốt nhất,
              chúng tôi không ngừng cập nhật những bộ truyện hot nhất từ khắp châu Á, từ những siêu
              phẩm manga kinh điển đến các manhwa, manhua đang gây sốt.
            </p>
          </div>

          <!-- Section 2 -->
          <div>
            <h2 class="text-xl font-semibold mb-3 flex items-center">
              Kho Truyện Đa Dạng - Cập Nhật Liên Tục 24/7
            </h2>
            <div class="grid md:grid-cols-2 gap-4">
              <div>
                <h3 class="font-medium mb-2">Manga Nhật Bản</h3>
                <p class="text-sm">
                  Từ One Piece, Naruto, Attack on Titan đến những bộ truyện mới nhất như Jujutsu
                  Kaisen, Demon Slayer. Tất cả đều được cập nhật nhanh chóng với chất lượng hình ảnh
                  cao.
                </p>
              </div>
              <div>
                <h3 class="font-medium mb-2">Manhwa Hàn Quốc</h3>
                <p class="text-sm">
                  Khám phá thế giới manhwa với Solo Leveling, Tower of God, The God of High School
                  và hàng trăm tựa truyện hấp dẫn khác với phong cách nghệ thuật độc đáo.
                </p>
              </div>
              <div>
                <h3 class="font-medium mb-2">Manhua Trung Quốc</h3>
                <p class="text-sm">
                  Các bộ Võ luyện đỉnh phong, phàm nhân tu tiên, tiên nghịch và nhiều series tu
                  tiên, huyền huyễn đầy kịch tính.
                </p>
              </div>
            </div>
          </div>

          <!-- Section 4 - Extended Content -->
          <div>
            <h2 class="text-xl font-semibold mb-3 flex items-center">
              Vì Sao Chọn MeTruyenMoi Là Website Đọc Truyện Tranh Tốt Nhất?
            </h2>
            <div class="space-y-4">
              <p>
                <strong>1. Chất lượng hàng đầu Việt Nam:</strong> Tất cả truyện tranh đều được scan
                và dịch thuật chất lượng HD cao, đảm bảo độc giả có trải nghiệm đọc manga, manhwa,
                manhua tốt nhất. Hình ảnh sắc nét, font chữ dễ đọc, bố cục chuyên nghiệp không lag,
                không quảng cáo phủ màn hình.
              </p>
              <p>
                <strong>2. Cập nhật nhanh nhất thị trường:</strong> Hệ thống làm việc 24/7 để mang
                đến những chapter mới nhất sớm nhất có thể. Nhiều series được cập nhật chỉ vài giờ
                sau bản gốc từ Nhật, Hàn, Trung. Hệ thống thông báo push tự động sẽ báo cho bạn ngay
                khi có chapter mới của truyện yêu thích theo dõi.
              </p>
              <p>
                <strong>3. Hoàn toàn miễn phí không giới hạn:</strong> Không phí đăng ký, không phí
                premium VIP, không giới hạn lượt đọc hàng ngày. MeTruyenMoi cam kết mang đến trải
                nghiệm đọc truyện tranh miễn phí cho tất cả mọi người Việt Nam và thế giới. Chúng
                tôi tin rằng manga, manhwa, manhua hay nên được mọi người tiếp cận một cách dễ dàng
                và thoải mái nhất.
              </p>
              <p>
                <strong>4. An toàn và bảo mật tuyệt đối:</strong> Website được bảo vệ bởi công nghệ
                bảo mật tiên tiến SSL/TLS, đảm bảo thông tin cá nhân của người dùng được an toàn
                tuyệt đối. Không virus, không malware, hệ thống mã hóa mọi dữ liệu truyền tải, bảo
                vệ quyền riêng tư của bạn khi đọc truyện online.
              </p>
              <p>
                <strong>5. Tương thích mọi thiết bị:</strong> Giao diện responsive hoàn hảo trên PC,
                laptop, tablet, smartphone. Đọc mượt mà trên mọi trình duyệt Chrome, Firefox,
                Safari, Edge. Tối ưu Dark Mode bảo vệ mắt khi đọc ban đêm.
              </p>
            </div>
          </div>

          <!-- Section 5 - Extended Content -->
          <div>
            <h2 class="text-xl font-semibold mb-3">Thể Loại Truyện Đa Dạng Cho Mọi Sở Thích</h2>
            <div class="grid md:grid-cols-3 gap-4 text-sm mb-6">
              <div class="space-y-2">
                <ul class="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• Action - Hành động</li>
                  <li>• Adventure - Phiêu lưu</li>
                  <li>• Fantasy - Viễn tưởng</li>
                  <li>• Supernatural - Siêu nhiên</li>
                  <li>• Isekai - Chuyển sinh</li>
                  <li>• Battle - Chiến đấu</li>
                </ul>
              </div>
              <div class="space-y-2">
                <ul class="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• Romance - Lãng mạn</li>
                  <li>• School Life - Học đường</li>
                  <li>• Slice of Life - Đời thường</li>
                  <li>• Drama - Tâm lý</li>
                  <li>• Josei - Phụ nữ trưởng thành</li>
                  <li>• Shoujo - Thiếu nữ</li>
                </ul>
              </div>
              <div class="space-y-2">
                <ul class="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• Horror - Kinh dị</li>
                  <li>• Mystery - Trinh thám</li>
                  <li>• Comedy - Hài hước</li>
                  <li>• Historical - Lịch sử</li>
                  <li>• Psychological - Tâm lý học</li>
                  <li>• Sci-Fi - Khoa học viễn tưởng</li>
                </ul>
              </div>
            </div>
            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
              Với hệ thống phân loại thông minh, bạn có thể dễ dàng tìm kiếm truyện theo sở thích cá
              nhân. Mỗi thể loại đều có những đặc trưng riêng, từ những câu chuyện hành động kịch
              tính đến những mẩu chuyện tình yêu ngọt ngào, hay những tác phẩm trinh thám đầy bí ẩn.
            </p>
          </div>

          <!-- Section 7 - Technology & Performance -->
          <div>
            <h2 class="text-xl font-semibold mb-3 flex items-center">
              Công Nghệ Tiên Tiến - Hiệu Suất Vượt Trội
            </h2>
            <div class="space-y-4">
              <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                <strong>Tối ưu hóa cho mọi thiết bị:</strong> Website được thiết kế responsive hoàn
                hảo, tự động điều chỉnh giao diện phù hợp với màn hình điện thoại, tablet và máy
                tính. Trải nghiệm đọc truyện mượt mà trên mọi platform, từ iOS, Android đến Windows,
                macOS.
              </p>
              <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                <strong>Tốc độ tải siêu nhanh:</strong> Hệ thống CDN toàn cầu giúp tải hình ảnh với
                tốc độ lightning-fast, giảm thiểu thời gian chờ đợi. Công nghệ nén ảnh thông minh
                đảm bảo chất lượng cao nhưng dung lượng tối ưu, tiết kiệm data cho người dùng
                mobile.
              </p>
              <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                <strong>Chế độ đọc offline:</strong> Tính năng cache thông minh cho phép lưu trữ các
                chapter đã đọc để xem offline, hoàn hảo cho những chuyến đi dài hoặc khi kết nối
                internet không ổn định.
              </p>
            </div>
          </div>

          <!-- CTA Section -->
          <div
            class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6 text-center"
          >
            <h2 class="text-xl font-bold mb-2">Bắt Đầu Hành Trình Khám Phá Ngay Hôm Nay!</h2>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              Hãy tham gia cộng đồng các độc giả đang yêu thích MeTruyenMoi. Khám phá, đọc và thưởng
              thức những bộ truyện tranh hay nhất hoàn toàn miễn phí! Đăng ký tài khoản để nhận được
              trải nghiệm cá nhân hóa và không bỏ lỡ bất kỳ update nào.
            </p>
            <div class="flex flex-wrap justify-center gap-2 text-sm mb-4">
              <span
                class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full"
              >
                #TruyenTranh
              </span>
              <span
                class="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full"
              >
                #Manga
              </span>
              <span
                class="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full"
              >
                #Manhwa
              </span>
              <span
                class="px-3 py-1 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded-full"
              >
                #MeTruyenMoi
              </span>
              <span
                class="px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-full"
              >
                #DocTruyenMienPhi
              </span>
              <span
                class="px-3 py-1 bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 rounded-full"
              >
                #CommunityFirst
              </span>
            </div>
          </div>
        </div>

        <!-- Fade Overlay when content is collapsed -->
        <div
          class="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-white via-white/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 pointer-events-none transition-opacity duration-300"
          [class.opacity-0]="isContentExpanded"
        ></div>
      </div>

      <!-- Read More Button -->
      <div
        *ngIf="!isContentExpanded"
        class="absolute bottom-0 left-1/2 -translate-x-1/2 text-center mt-6"
      >
        <button
          (click)="toggleContent()"
          class="inline-flex items-center px-6 py-3 text-blue-600 dark:text-blue-400 hover:shadow-md"
        >
          <span>Đọc thêm</span>
          <svg
            class="w-4 h-4 ml-2 transition-transform duration-200"
            [class.rotate-180]="isContentExpanded"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
