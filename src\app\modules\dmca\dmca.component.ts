import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { SeoService } from '@services/seo.service';

@Component({
  selector: 'main[app-dmca]',
  templateUrl: './dmca.component.html',
  styleUrls: ['./dmca.component.scss'],
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DmcaComponent implements OnInit {
  constructor(private seo: SeoService) {}
  ngOnInit(): void {
    this.seo.setDmcaSEO();
  }
}
