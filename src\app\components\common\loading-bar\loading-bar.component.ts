import { CommonModule, isPlatformServer } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  Inject,
  NgZone,
  OnInit,
  PLATFORM_ID,
  ViewEncapsulation,
  computed,
  inject,
  signal
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  NavigationCancel,
  NavigationEnd,
  NavigationError,
  NavigationStart,
  ResolveEnd,
  Router
} from '@angular/router';
import { LoadingService } from '@services/loading.service';
import { timer } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
@Component({
  selector: 'div[app-loading-bar]',
  templateUrl: './loading-bar.component.html',
  styleUrl: './loading-bar.component.scss',
  standalone: true,
  imports: [CommonModule],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoadingBarComponent implements OnInit {
  // Dependency injection using modern inject function
  private readonly loadingService = inject(LoadingService);
  private readonly router = inject(Router);
  private readonly platformId = inject(PLATFORM_ID);
  private readonly destroyRef = inject(DestroyRef);
  private readonly ngZone = inject(NgZone);

  // Reactive state management with signals
  private readonly isLoadingSignal = signal(false);
  private readonly progressSignal = signal(0);
  private readonly isDoneSignal = signal(false);
  private readonly navigationEndSignal = signal(false);

  // Public observables for template
  readonly isLoading$ = this.isLoadingSignal.asReadonly();
  readonly progress$ = this.progressSignal.asReadonly();

  // Internal state
  private maxTasks = 0;
  private currentProgress = 0;
  private firstNavigation = true;
  ngOnInit(): void {
    if (isPlatformServer(this.platformId)) return;

    this.setupRouterEvents();
    this.setupTaskTracking();
  }

  /**
   * Setup router event tracking for navigation progress
   */
  private setupRouterEvents(): void {
    this.router.events
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((event) => {
        if (this.firstNavigation) {
          this.firstNavigation = false;
        } else if (event instanceof NavigationStart) {
          this.startLoading();
        } else if (event instanceof NavigationCancel || event instanceof NavigationError) {
          this.stopLoading();
        } else if (event instanceof ResolveEnd) {
          this.incrementProgress(5);
        } else if (event instanceof NavigationEnd) {
          this.handleNavigationEnd();
        } else {
          this.incrementProgress(1);
        }
      });
  }

  /**
   * Setup task tracking for async operations
   */
  private setupTaskTracking(): void {
    this.loadingService.tasks$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        debounceTime(50) // Debounce rapid task changes
      )
      .subscribe((tasks) => {
        this.updateTaskProgress(tasks);
      });
  }

  /**
   * Start loading state
   */
  private startLoading(): void {
    this.isLoadingSignal.set(true);
    this.isDoneSignal.set(false);
    this.progressSignal.set(5);
    this.navigationEndSignal.set(false);
    this.currentProgress = 5;
  }

  /**
   * Stop loading state
   */
  private stopLoading(): void {
    this.isLoadingSignal.set(false);
    this.progressSignal.set(0);
    this.currentProgress = 0;
  }

  /**
   * Increment progress by specified amount
   */
  private incrementProgress(amount: number): void {
    this.currentProgress = Math.min(this.currentProgress + amount, 90);
    this.progressSignal.set(this.currentProgress);
  }

  /**
   * Handle navigation end event
   */
  private handleNavigationEnd(): void {
    this.maxTasks = this.loadingService.tasks.length;
    this.navigationEndSignal.set(true);
    this.updateTaskProgress(this.loadingService.tasks);
  }

  /**
   * Update progress based on remaining tasks
   */
  private updateTaskProgress(tasks: string[]): void {
    if (!this.navigationEndSignal() || this.isDoneSignal()) return;

    const taskLength = tasks.length;

    if (this.maxTasks > 0) {
      const taskProgress = ((this.maxTasks - taskLength) / this.maxTasks) * (90 - this.currentProgress);
      const newProgress = Math.min(this.currentProgress + taskProgress, 90);
      this.progressSignal.set(newProgress);
    }

    if (taskLength === 0) {
      this.ngZone.runOutsideAngular(() => {
        this.completeLoading();
      });
    }
  }

  /**
   * Complete loading with smooth animation
   */
  private completeLoading(): void {
    this.isDoneSignal.set(true);

    // Animate to 100%
    timer(200)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.progressSignal.set(100);

        // Hide loading bar after animation
        timer(500)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe(() => {
            this.stopLoading();
          });
      });
  }

  // Public computed properties for template
  readonly isLoading = this.isLoadingSignal.asReadonly();
  readonly progress = this.progressSignal.asReadonly();
  readonly isIndeterminate = computed(() =>
    this.isLoadingSignal() && this.progressSignal() === 0
  );
}
