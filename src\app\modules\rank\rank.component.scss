// Subtle animations for ranking
@keyframes gentlePulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes subtleFloat {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// Main ranking container - lighter design
.ranking-container {
  @apply bg-white dark:bg-neutral-900;
  @apply border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm p-6;
}

// Simplified ranking header
.ranking-header {
  @apply mb-6 flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4;
}

.ranking-title-section {
  @apply flex items-center gap-2;
}

.ranking-title {
  @apply text-xl uppercase font-extrabold leading-3 min-h-full text-gray-700 dark:text-light-text mt-0.5;
}

// Simplified filter controls
.ranking-filters {
  @apply flex flex-wrap gap-3 items-center;
}

.filter-group {
  @apply flex flex-col gap-1;
}

.filter-label {
  @apply text-sm font-medium text-neutral-600 dark:text-neutral-400;
}

.filter-select {
  @apply w-full sm:w-52  bg-white dark:bg-neutral-800;
  @apply rounded-lg px-3 py-2 text-sm text-neutral-700 dark:text-neutral-300;
}
