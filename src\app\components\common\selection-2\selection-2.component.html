<div
  (appClickOutside)="closeDropdown()"
  class="relative inline-flex w-full items-center text-black dark:text-light-text"
  [ngClass]="size"
>
  <button
    [title]="this.selectedIdx >= 0 ? options[this.selectedIdx].label : 'None'"
    class="w-full flex justify-between px-2 py-1 rounded-md transition bg-gray-100 dark:bg-neutral-700 outline outline-1 focus:outline-primary-100 focus:text-primary-100 font-medium"
    [ngClass]="{
      'outline outline-primary-100': isDropdownOpen,
      'hover:outline hover:outline-primary-100': !isDropdownOpen
    }"
    (click)="toggleDropdown()"
  >
    <ng-container [ngTemplateOutlet]="customBtn || selectButton" />
  </button>
  <ul
    [ngClass]="{ hidden: !isDropdownOpen }"
    class="scrollbar-style-1 flex absolute flex-col mt-1 border w-full max-h-96 overflow-y-auto z-50 left-0 rounded-md shadow-md py-1 top-full bg-white dark:bg-neutral-800 dark:border-gray-500"
  >
    <li
      *ngFor="let option of options; let i = index"
      (click)="selectOption(option)"
      [ngClass]="{
        ' bg-primary-100 text-white': i === selectedIdx,
        'hover:bg-gray-200 hover:text-secondary-100 dark:hover:bg-neutral-700 dark:hover:text-white':
          i !== selectedIdx
      }"
      class="grid grid-cols-[0.75rem_1fr] py-1 px-2 cursor-pointer transition-colors mx-1 rounded-md my-0.5 gap-2 items-center"
    >
        <svg class="size-3" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path
            stroke-width="2"
            d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
          ></path>
        </svg>
        {{ option.label }}
    </li>
  </ul>
</div>

<ng-template #selectButton>
  <span class="text-left truncate font-semibold">
    {{ this.selectedIdx >= 0 ? options[this.selectedIdx].label : 'None' }}
  </span>
  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 my-auto" viewBox="0 0 24 24">
    <path
      fill="none"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="m8 9 4-4 4 4m0 6-4 4-4-4"
    ></path>
  </svg>
</ng-template>

<ng-content />
