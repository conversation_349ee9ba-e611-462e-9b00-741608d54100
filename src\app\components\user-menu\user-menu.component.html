<div class="user-menu-wrapper" (appClickOutside)="closeMenu()">
  <!-- Authenticated User Trigger -->
  <button
    *ngIf="isAuthenticated; else guestUserTrigger"
    (click)="toggleMenu()"
    class="user-menu-trigger authenticated"
    [class.active]="isOpen()"
    aria-label="User menu"
    [attr.aria-expanded]="isOpen()"
  >
    <div class="user-avatar-container">
      <img
        loading="lazy"
        class="user-avatar"
        [src]="user?.avatar || 'default_avatar.jpg'"
        onerror="this.src='/default_avatar.jpg'"
        alt="User avatar"
      />
      <div class="user-status-indicator"></div>
    </div>
  </button>
  <ng-template #guestUserTrigger>
    <button
      (click)="toggleMenu()"
      class="user-menu-trigger guest"
      [class.active]="isOpen()"
      aria-label="Guest user menu"
      [attr.aria-expanded]="isOpen()"
    >
      <div class="guest-avatar-container">
        <svg class="guest-avatar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
      </div>
    </button>
  </ng-template>
  <ng-container #userMenu></ng-container>

  <div *ngIf="isOpen()" class="dropdown-backdrop" (click)="closeMenu()"></div>

  <!-- Guest User Trigger -->
</div>
