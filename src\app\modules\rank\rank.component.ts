import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';

import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic, ComicStatus, SortType } from '@schema';
import { ComicService } from '@services/comic.service';
import { SEOData, SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import {
  IFilters,
  rankFiltersOptions,
} from '../../components/utils/constants';

@Component({
  selector: 'main[app-rank]',
  templateUrl: './rank.component.html',
  styleUrl: './rank.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RankComponent extends OptimizedBaseComponent implements OnInit {
  listComics: Comic[] = [];
  // listTopComics!: Comic[];

  totalpage!: number;
  totalResult!: number;
  currentPage = 1;
  dataView!: IFilters;

  selectedComic?: Comic;
  isShowDetails = false;

  selectOptions: any = {
    sorts: { value: 8, name: 'Top All' },
    status: { value: -1, name: 'Tất cả' },
  };
  queryParams: Params = {};
  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private urlService: UrlService,
    override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID)  override platformId: object,
  ) {
    super(cd, platformId);
    this.dataView = {
      status: rankFiltersOptions.status,
      sorts: rankFiltersOptions.sorts,
    };
    // Remove duplicate setupSeo call - it will be called in ngOnInit with page parameter
  }


  ngOnInit(): void {
    this.route.queryParams.pipe(this.takeUntilDestroy()).subscribe((params: any) => {
      const page = Number(params['page']) || 1;
      const status =
        Number(params['status']) >= 0
          ? Number(params['status'])
          : ComicStatus.ALL;
      const sort =
        Number(params['sort']) >= 0 ? Number(params['sort']) : SortType.TopAll;

      this.currentPage = page;
      this.onSearchComic(page, sort, status);
    });
  }


  onSearchComic(page: number, sort: number, status: number) {
    this.comicService
      .getComics({
        step: '35',
        genre: '-1',
        page: page.toString(),
        sort: sort.toString(),
        status: status.toString(),
      })
      .pipe(this.takeUntilDestroy())
      .subscribe((res: any) => {
        this.totalpage = res.data.totalpage;
        this.listComics = res.data.comics;

        // Show details of the first comic if not already selected
        if (!this.selectedComic && this.listComics.length > 0) {
          this.showDetails(this.listComics[0]);
        }
        // this.getTopComics();
        this.setupSeo();
        this.safeMarkForCheck();

      });
  }

  // getTopComics() {
  //   if (!this.listComics) return;
  //   this.listTopComics = this.listComics.slice(0, 5);
  // }

  onSortOptionChange(value: number) {
    this.selectOptions.sorts.value = value;
    this.queryParams = {
      ...this.queryParams,
      sort: value,
      page: 1,
    };
    this.router.navigate([], {
      queryParams: this.queryParams,
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }
  onStatusOptionChange(value: number) {
    this.selectOptions.status.value = value;
    this.queryParams = {
      ...this.queryParams,
      status: value,
      page: 1,
    };
    this.router.navigate([], {
      queryParams: this.queryParams,
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }


  showDetails(comic: Comic) {
    this.selectedComic = comic;
  }
  setupSeo() {
    // Use the new comprehensive SEO method
    this.seoService.setRankingSEO(this.listComics.slice(0, 20), this.currentPage);
  }
}
