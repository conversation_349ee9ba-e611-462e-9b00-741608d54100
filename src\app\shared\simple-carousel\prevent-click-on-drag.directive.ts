import { Directive, ElementRef, Input, OnD<PERSON>roy, OnInit } from '@angular/core';

@Directive({
  selector: '[preventClickOnDrag]',
  standalone: true
})
export class PreventClickOnDragDirective implements OnInit, OnDestroy {
  @Input() dragThreshold = 5;

  private startX = 0;
  private startY = 0;
  private dragged = false;

  private mouseDownHandler = (event: MouseEvent | TouchEvent) => {
    this.dragged = false;
    if (event instanceof MouseEvent) {
      this.startX = event.clientX;
      this.startY = event.clientY;
    } else if (event.touches.length) {
      this.startX = event.touches[0].clientX;
      this.startY = event.touches[0].clientY;
    }
  };

  private mouseMoveHandler = (event: MouseEvent | TouchEvent) => {
    let x = 0, y = 0;
    if (event instanceof MouseEvent) {
      x = event.clientX;
      y = event.clientY;
    } else if (event.touches.length) {
      x = event.touches[0].clientX;
      y = event.touches[0].clientY;
    }

    if (Math.abs(x - this.startX) > this.dragThreshold ||
      Math.abs(y - this.startY) > this.dragThreshold) {
      this.dragged = true;
    }
  };

  private clickHandler = (event: MouseEvent) => {
    if (this.dragged) {
      event.preventDefault();
      event.stopImmediatePropagation();
    }
  };

  constructor(private el: ElementRef<HTMLElement>) { }

  ngOnInit(): void {
    const nativeEl = this.el.nativeElement;
    nativeEl.addEventListener('mousedown', this.mouseDownHandler, { passive: true });
    nativeEl.addEventListener('touchstart', this.mouseDownHandler, { passive: true });
    nativeEl.addEventListener('mousemove', this.mouseMoveHandler, { passive: true });
    nativeEl.addEventListener('touchmove', this.mouseMoveHandler, { passive: true });
    nativeEl.addEventListener('click', this.clickHandler, true,); // capture phase để chặn sớm
  }

  ngOnDestroy(): void {
    const nativeEl = this.el.nativeElement;
    nativeEl.removeEventListener('mousedown', this.mouseDownHandler);
    nativeEl.removeEventListener('touchstart', this.mouseDownHandler);
    nativeEl.removeEventListener('mousemove', this.mouseMoveHandler);
    nativeEl.removeEventListener('touchmove', this.mouseMoveHandler);
    nativeEl.removeEventListener('click', this.clickHandler, true);
  }
}
