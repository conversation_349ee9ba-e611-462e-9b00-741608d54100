<!-- Modern Chat Box -->
<ng-container *ngIf="isShowChatBox">
  <div class="chat-box-container">
    <div class="chat-box-wrapper">
      <!-- Modern Chat Menu -->
      <div
        #chatMenu
        class="chat-menu"
        [class.chat-menu-hidden]="!isShowChatMenu"
      >
        <div class="chat-menu-header">
          <h3 class="chat-menu-title">Tin nhắn</h3>
          <div class="chat-menu-badge">{{ conversations.length}}</div>
        </div>

        <div class="chat-users-container scrollbar-style-1">
          <!-- Real Conversations -->
          <ng-container *ngFor="let conversation of conversations">
            <button
              type="button"
              class="chat-user-item"
              [class.chat-user-active]="currentConversation && currentConversation.id === conversation.id"
              (click)="selectConversation(conversation)"
            >
              <div class="chat-user-avatar-wrapper">
                <img
                  [src]="conversation.icon"
                  class="chat-user-avatar"
                />
                <div class="chat-user-status"></div>
              </div>

              <div class="chat-user-info">
                <div class="chat-user-details">
                  <h4 class="chat-user-name">
                    {{ conversation.name }}
                  </h4>
                  <p class="chat-user-preview">
                    {{ conversation.channel == 0 ? 'Chat với admin' : 'Chat với AI Moi' }}
                  </p>
                </div>
                <div class="chat-user-meta">
                  <span class="chat-user-time">
                    {{ conversation.lastMessage?.createdAt | date:'short' }}
                  </span>
                </div>
              </div>
            </button>
          </ng-container>

          
        </div>
      </div>
      <!-- Mobile Overlay -->
      <div
        class="chat-overlay"
        [class.chat-overlay-hidden]="!isShowChatMenu"
        (click)="isShowChatMenu = false"
      ></div>

      <!-- Modern Chat Main Area -->
      <div class="chat-main-area">
        <!-- Modern Chat Header -->
        <div class="chat-header">
          <div class="chat-header-left">
            <button
              type="button"
              class="chat-menu-toggle"
              (click)="isShowChatMenu = !isShowChatMenu"
              title="Toggle menu"
            >
              <svg class="chat-menu-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="3" y1="6" x2="21" y2="6"/>
                <line x1="3" y1="12" x2="21" y2="12"/>
                <line x1="3" y1="18" x2="21" y2="18"/>
              </svg>
            </button>

            <div class="chat-header-user">
              <div class="chat-header-avatar-wrapper">
                <img
                  [src]="currentConversation?.icon"
                  class="chat-header-avatar"
                />
                <div class="chat-header-status"></div>
              </div>
              <div class="chat-header-info">
                <h3 class="chat-header-name">
                  {{
                    currentConversation?.name
                  }}
                </h3>
                <p class="chat-header-status-text">Đang hoạt động</p>
              </div>
            </div>
          </div>

          <div class="chat-header-actions">
            <button
              type="button"
              class="chat-action-btn"
              title="Tùy chọn"
            >
              <svg class="chat-action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="1"/>
                <circle cx="12" cy="5" r="1"/>
                <circle cx="12" cy="19" r="1"/>
              </svg>
            </button>

            <button
              type="button"
              class="chat-action-btn chat-close-btn"
              (click)="closeChat()"
              title="Đóng chat"
            >
              <svg class="chat-action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>
        </div>
        <!-- Modern Chat Messages -->
        <div #chatBox class="chat-messages-container">
          <!-- Real Messages from Current Conversation -->
          <ng-container *ngIf="currentConversation && messages && messages.length">
            <div *ngFor="let message of messages; let last = last" class="message-wrapper">
              <div
                class="message-item"
                [class.message-sent]="loginUser && message.userId === loginUser.id"
                [class.message-received]="!loginUser || message.userId !== loginUser.id"
              >
                <div class="message-avatar">
                  <img
                    [src]="message.user?.avatar || '/default-avatar.png'"
                    [alt]="message.user?.firstName || 'User'"
                    class="message-avatar-img"
                  />
                </div>

                <div class="message-content">
                  <div class="message-bubble" [innerHTML]="message.content"></div>
                  <div class="message-time">
                    {{ message.createdAt | date:'short' }}
                  </div>
                  <!-- Loading spinner for bot message -->
                  <ng-container *ngIf="isBotLoading && last && message.user?.firstName === 'Chat Bot'">
                    <div class="message-bot-loading" style="display: flex; align-items: center; gap: 8px; margin-top: 4px;">
                      <span class="spinner animate-spin" style="width: 18px; height: 18px; border: 2px solid #ccc; border-top: 2px solid #007bff; border-radius: 50%; display: inline-block; animation: spin 1s linear infinite;"></span> Đang trả lời...
                    </div>
                  </ng-container>
                </div>
              </div>
            </div>
          </ng-container>

        

          <!-- Empty State -->
          <!-- <div *ngIf="(!currentConversation || !messages || !messages.length) && (!selectedUser?.messages || !selectedUser.messages.length)" class="chat-empty-state">
            <div class="empty-state-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
              </svg>
            </div>
            <h3 class="empty-state-title">Chưa có tin nhắn</h3>
            <p class="empty-state-subtitle">
              Hãy bắt đầu cuộc trò chuyện với
              {{ currentConversation ?
                (currentConversation.name || getOtherUser(currentConversation)?.firstName) :
                selectedUser?.name }}
            </p>
          </div> -->
        </div>
        <!-- Modern Chat Input -->
        <div class="chat-input-container">
          <div class="chat-input-wrapper">
            <!-- Emoji Button -->
            <div class="chat-input-actions">
              <button
                type="button"
                class="chat-input-btn emoji-btn"
                (appClickOutside)="activeEmojiPicker = false"
                title="Chọn emoji"
              >
                <svg
                  class="chat-input-icon"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  (click)="toggleEmojiPicker()"
                >
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                  <line x1="9" y1="9" x2="9.01" y2="9"/>
                  <line x1="15" y1="9" x2="15.01" y2="9"/>
                </svg>

                <!-- Emoji Picker -->
                <div *ngIf="activeEmojiPicker" class="emoji-picker-wrapper">
                  <div
                    app-emoji
                    class="emoji-picker"
                    (emojiSelect)="addEmoji($event)"
                  ></div>
                </div>
              </button>
            </div>

            <!-- Message Input -->
            <div class="chat-input-field-wrapper">
              <input
                name="textMessage"
                type="text"
                class="chat-input-field"
                placeholder="Nhập tin nhắn..."
                [value]="textMessage"
                (input)="msgChange($event)"
                (keyup.enter)="sendMessage()"
                name="textMessage"
                autocomplete="off"
              />
            </div>

            <!-- Send Button -->
            <button
              type="button"
              class="chat-send-btn"
              (click)="sendMessage()"
              [disabled]="!textMessage?.trim()"
              title="Gửi tin nhắn"
            >
              <svg class="chat-send-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="22" y1="2" x2="11" y2="13"/>
                <polygon points="22,2 15,22 11,13 2,9 22,2"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>
