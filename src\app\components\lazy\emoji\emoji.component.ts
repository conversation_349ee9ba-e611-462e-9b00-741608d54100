import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Output, ViewEncapsulation, computed, signal } from '@angular/core';
import { EMOJI_CONTENTS } from './utils/constants';
import { IPopupComponent } from 'src/app/core/interface';

@Component({
    selector: '[app-emoji]',
    templateUrl: './emoji.component.html',
    styleUrl: './emoji.component.scss',
    standalone: true,
    imports: [CommonModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
    encapsulation: ViewEncapsulation.None,
})
export class EmojiComponent implements IPopupComponent {

  // Signals for reactive state management
  private readonly activateSignal = signal<number>(1);
  private readonly isVisibleSignal = signal<boolean>(false);

  // Computed properties for better performance
  readonly emoji_contents = EMOJI_CONTENTS;
  readonly activate = computed(() => this.activateSignal());
  readonly isVisible = computed(() => this.isVisibleSignal());
  readonly activeEmoji = computed(() =>
    this.emoji_contents.find(content => content.id === this.activateSignal())
  );
  readonly emojiList = computed(() => {
    const active = this.activeEmoji();
    return active ? Array.from({ length: active.length }, (_, i) => i + 1) : [];
  });
  readonly visibleEmojiPacks = computed(() => this.emoji_contents.slice(0, 5));
  readonly hiddenEmojiPacks = computed(() => this.emoji_contents.slice(5));

  constructor(private cd: ChangeDetectorRef) { }

  @Output() emojiSelect = new EventEmitter<{ name: string; path: string }>();

  // TrackBy functions for performance optimization
  trackByEmojiId = (_index: number, emoji: any): number => emoji.id;
  trackByEmojiIndex = (_index: number, item: number): number => item;

  selectPacket(activate_id: number): void {
    this.activateSignal.set(activate_id);
    if (this.isVisible()) {
      this.togglePopover();
    }
  }

  show(_object: any): Promise<any> {
    this.setVisible(true);
    return new Promise((resolve) => {
      resolve({});
    });
  }

  setVisible(isVisible: boolean): void {
    this.isVisibleSignal.set(isVisible);
    this.cd.detectChanges();
  }

  selectEmoji(name: string, path: string): void {
    this.emojiSelect.emit({ name, path });
  }

  handleEmojiClick(index: number): void {
    const activeEmoji = this.activeEmoji();
    if (!activeEmoji) return;

    const name = this.getAttrTarget(activeEmoji.name, index);
    const path = this.getEmojiPath(this.activate(), index);

    this.selectEmoji(name, path);
  }

  getAttrTarget(name: string, index: number): string {
    return `${name}_${index}`;
  }

  togglePopover(event?: Event): void {
    if (event) {
      event.stopPropagation(); // Ngăn sự kiện lan lên cha
    }
    this.setVisible(!this.isVisible());
  }

  getEmojiPath(activate: number, index: number): string {
    const activeEmoji = this.emoji_contents.find(
      (content) => content.id === activate
    );
    if (!activeEmoji || index <= 0 || index > activeEmoji.length) return '';

    return `/emoji/data/${activeEmoji.name}/${index}.gif`;
  }

  // Legacy method for backward compatibility - now uses computed property
  getEmojiList(): number[] {
    return this.emojiList();
  }
}
