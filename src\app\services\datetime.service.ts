import { Injectable } from "@angular/core";

@Injectable({
  providedIn: 'root',
})
export class DateTimeService {


  getDateTime(): string {
    return new Date().toLocaleString();
  }
  formatDateTo(date: Date): string {
    // Ensure the date is valid
    if (isNaN(date.getTime())) {
      throw new Error("Invalid date string");
    }

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = date.getFullYear();
    // const hours = String(date.getHours()).padStart(2, '0');
    // const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${day}/${month}/${year}`;
    // switch (format) {
    //   case "DD/MM/YYYY":
    //     return `${day}/${month}/${year}`;
    //   case "DD/MM/YYYY HH:mm":
    //     return `${day}/${month}/${year} ${hours}:${minutes}`;

    // }
    // return `${day}/${month}/${year} ${hours}:${minutes}`;
  }

}