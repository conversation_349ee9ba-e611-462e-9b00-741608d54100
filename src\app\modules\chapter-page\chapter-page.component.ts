import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnDestroy,
  OnInit,
  Optional,
  PLATFORM_ID,
  RESPONSE_INIT,
  Renderer2,
  ViewChild,
  computed,
  signal,
} from '@angular/core';

import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ChapterSelectorComponent } from '@components/common/chapter-selector/chapter-selector.component';
import { AdsModule } from 'src/app/shared/ads.module';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { AnouncementComponent } from '@components/common/anouncement/anouncement.component';
import { CommentComponent } from '@components/common/comment/comment.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { BaseModalComponent } from '@components/common/base-modal/base-modal.component';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { BreadcrumbLink } from '@components/common/breadcrumb/breadcrumb.component';
import { SettingCategory } from '@components/lazy/app-setting/interfaces/setting-interfaces';
import { ChatBoxBubbleComponent } from '@components/lazy/chat-box/chat-box-bubble/chat-box-bubble.component';
import { Chapter, ChapterPage, ChapterServer, Comic, UserExpType } from '@schema';
import { ComicService } from '@services/comic.service';
import { HistoryService } from '@services/history.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { SettingService } from '@services/setting.service';
import { UrlService } from '@services/url.service';
import { throttle } from 'src/app/utils';
export enum StickyState {
  NoneSticky = 0,
  Sticky = 1,
  StickyInvisible = 3,
}
@Component({
  selector: 'main[app-chapter]',
  templateUrl: './chapter-page.component.html',
  styleUrl: './chapter-page.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ReactiveFormsModule,
    AdsModule,
    BreadcrumbComponent,
    AnouncementComponent,
    // FadeInDirective,
    // PopoverDirective,
    CommentComponent,
    ChapterSelectorComponent,
    // OptimizedImageComponent,
    BaseModalComponent,
    // SelectionComponent,
    ClickOutsideDirective,
    CommonModule,
    RouterModule
  ],
  
})
export class ChapterPageComponent
  extends OptimizedBaseComponent
  implements AfterViewInit, AfterViewChecked, OnDestroy, OnInit {

  readonly bannerImg: string = '/banner/banner-manga-4.webp';

  // Signals for reactive state management
  private readonly listImgsSignal = signal<string[] | undefined>(undefined);
  private readonly listChapterServersSignal = signal<ChapterServer[]>([]);
  private readonly comicSignal = signal<Comic | undefined>(undefined);
  private readonly mainChapterSignal = signal<ChapterPage | undefined>(undefined);

  // Control Bar State Management Signals
  private readonly stickyStateSignal = signal<StickyState>(StickyState.NoneSticky);
  private readonly controlBarVisibleSignal = signal<boolean>(true);
  private readonly animationTimeout = signal<number | undefined>(undefined);

  // Scroll Management Signals
  private readonly lastScrollTopSignal = signal<number>(0);
  private readonly isTrackingSignal = signal<boolean>(false);
  private readonly toolbarOriginalPositionSignal = signal<number>(0);
  private readonly showScrollToTopSignal = signal<boolean>(false);
  private readonly statePositionSignal = signal<number>(0);

  // Loading & Server Management Signals
  private readonly isImageLoadingSignal = signal<boolean>(true);
  private readonly selectedServerSignal = signal<ChapterServer>({ id: 0, images: [] });
  private readonly showAllServersSignal = signal<boolean>(false);
  private readonly scrollStateSignal = signal<string>('down');

  private readonly breadcrumbLinksSignal = signal<BreadcrumbLink[]>([]);

  // Chapter Settings Signal
  private readonly chapterSettingSignal = signal({
    isFullScreen: false,
    isNightMode: false,
    isAutoNextChapter: false,
    isVertical: true,
    preloadPages: 3,
    fixedToolbar: false,
    toolbarStyle: 'classic',
  });

  // Zoom Data Signal
  private readonly zoomDataSignal = signal({
    minZoomLevel: 50,
    maxZoomLevel: 150,
    zoomValue: 100,
    defaultZoomLevel: 100,
    isZoomIn: false,
  });

  // Computed properties for reactive access
  readonly listImgs = computed(() => this.listImgsSignal());
  readonly listChapterServers = computed(() => this.listChapterServersSignal());
  readonly comic = computed(() => this.comicSignal());
  readonly mainChapter = computed(() => this.mainChapterSignal());
  readonly stickyState = computed(() => this.stickyStateSignal());
  readonly isImageLoading = computed(() => this.isImageLoadingSignal());
  readonly selectedServer = computed(() => this.selectedServerSignal());
  readonly showAllServers = computed(() => this.showAllServersSignal());
  readonly chapterSetting = computed(() => this.chapterSettingSignal());
  readonly zoomData = computed(() => this.zoomDataSignal());
  readonly lastScrollTop = computed(() => this.lastScrollTopSignal());
  readonly isTracking = computed(() => this.isTrackingSignal());
  readonly toolbarOriginalPosition = computed(() => this.toolbarOriginalPositionSignal());
  readonly showScrollToTop = computed(() => this.showScrollToTopSignal());
  readonly statePosition = computed(() => this.statePositionSignal());
  readonly scrollState = computed(() => this.scrollStateSignal());
  readonly breadcrumbLinks = computed(() => this.breadcrumbLinksSignal());

  readonly getNextChapter = computed(() => {
    const comic = this.comic();
    const mainChapter = this.mainChapter();

    if (!comic?.chapters || !mainChapter) {
      return null;
    }

    const currentChapterIndex = comic.chapters.findIndex(
      (chapter: Chapter) => chapter.id === mainChapter.id
    );

    return currentChapterIndex > 0
      ? comic.chapters[currentChapterIndex - 1]
      : null;
  });

  readonly getPreviousChapter = computed(() => {
    const comic = this.comic();
    const mainChapter = this.mainChapter();

    if (!comic?.chapters || !mainChapter) {
      return null;
    }

    const currentChapterIndex = comic.chapters.findIndex(
      (chapter: Chapter) => chapter.id === mainChapter.id
    );

    return  currentChapterIndex < comic.chapters.length - 1
      ? comic.chapters[currentChapterIndex + 1]
      : null;
  });

  readonly previousChapterLink = computed(() => {
    const preChapter = this.getPreviousChapter();
    if (preChapter) {
      return this.urlService.getChapterDetailUrl(this.comic()!, preChapter);
    }
    return null;
  });
  readonly nextChapterLink = computed(() => {
    const nextChapter = this.getNextChapter();
    if (nextChapter) {
      return this.urlService.getChapterDetailUrl(this.comic()!, nextChapter);
    }
    return null;
  });
  // Computed derived values
  readonly imageWidth = computed(() => this.zoomData().zoomValue + '%');
  readonly imageLeft = computed(() => (100 - this.zoomData().zoomValue) * 0.5 + '%');
  readonly zoomPercentage = computed(() => Math.round(this.zoomData().zoomValue));
  readonly countdown = signal(5);
  errorImages = signal(0);
  intervalRef: any;
  isErrorPages = false;
  // isLoadChapters = false;

  // ViewChild elements (keeping minimal necessary ones)
  @ViewChild('screenContainer') screenContainer!: ElementRef;
  @ViewChild('imageContainer') imageContainer!: ElementRef;
  @ViewChild('controlBar') controlBar!: ElementRef;
  @ViewChild('controlBarContainer') controlBarContainer!: ElementRef;
  @ViewChild('EndChapter') endChapterElement!: ElementRef;
  @ViewChild('modalRoot') modal!: BaseModalComponent;
  

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private historyService: HistoryService,
    private seoService: SeoService,
    private renderer: Renderer2,
    protected override cd: ChangeDetectorRef,
    private popupService: PopupService,
    public urlService: UrlService,
    private settingService: SettingService,
    @Optional() @Inject(RESPONSE_INIT) private responseInit: ResponseInit,

    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }
  ngOnInit(): void {
    ChatBoxBubbleComponent.Instance?.SetVisible(false);
    this.runInBrowser(() => {
      this.loadSetting();
    });
    this.loadChapterPage();
  }

  // Helper methods to reduce direct ViewChild access
  private getScreenElement(): HTMLElement | null {
    return this.screenContainer?.nativeElement || null;
  }

  private getImageElement(): HTMLElement | null {
    return this.imageContainer?.nativeElement || null;
  }

  private getControlBarElement(): HTMLElement | null {
    return this.controlBar?.nativeElement || null;
  }

  private getControlBarContainerElement(): HTMLElement | null {
    return this.controlBarContainer?.nativeElement || null;
  }

  private getEndChapterElement(): HTMLElement | null {
    return this.endChapterElement?.nativeElement || null;
  }

  onOpenChapterSelector() {
    // if (!this.isLoadChapters)
    //   this.addSubscription(
    //     this.comicService
    //       .getChapters(this.comic()!.id)
    //       .pipe(this.takeUntilDestroy())
    //       .subscribe((res: any) => {
    //         this.comicSignal.update(current => ({
    //           ...current!,
    //           chapters: res.data || [],
    //         }));
    //         this.safeMarkForCheck();
    //       })
    //   );
    // this.isLoadChapters = true;
  }
  ngAfterViewInit(): void {
    if (this.isErrorPages) {
      this.autoChangeServerErrorModal();
    }
    this.runInBrowser(() => {
      const element = this.getControlBarContainerElement();
      if (element) {
        const rect = element.getBoundingClientRect();
        this.toolbarOriginalPositionSignal.set(rect.top + window.scrollY);
      }
    });
  }
  ngAfterViewChecked(): void { }
  override ngOnDestroy(): void {
    // Clear animation timeout to prevent memory leaks
    const timeoutId = this.animationTimeout();
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.animationTimeout.set(undefined);
    }

    ChatBoxBubbleComponent.Instance?.SetVisible(true);
    super.ngOnDestroy();
  }

  getImageWidth() {
    return this.imageWidth();
  }
  getImageLeft() {
    return this.imageLeft();
  }
  verifyZoom(value: number) {
    if (value <= 0) return 1;
    const zoomData = this.zoomData();
    value = Math.min(value, zoomData.maxZoomLevel);
    value = Math.max(value, zoomData.minZoomLevel);
    return value;
  }

  loadSetting() {
    const currentChapterSetting = this.chapterSetting();
    const currentZoomData = this.zoomData();

    this.chapterSettingSignal.set({
      ...currentChapterSetting,
      isFullScreen: this.settingService.getSettingValue('doubleClickToFullscreen') ?? false,
      isNightMode: this.settingService.getSettingValue('nightMode') ?? false,
      isAutoNextChapter: this.settingService.getSettingValue('autoNextChapter') ?? false,
      isVertical: this.settingService.getSettingValue('verticalReading') ?? true,
      preloadPages: this.settingService.getSettingValue('preloadPages') ?? 3,
      fixedToolbar: this.settingService.getSettingValue('fixedToolbar') ?? false,
    });

    this.zoomDataSignal.set({
      ...currentZoomData,
      zoomValue: this.verifyZoom(this.settingService.getSettingValue('zoom-reading') ?? 100),
    });
    this.chapterSettingSignal.update(current => ({
      ...current,
      toolbarStyle: this.settingService.getSettingValue('styleToolbar') ?? 'classic',
    }));

    this.addSubscription(
      this.settingService.settingChanges$.pipe(this.takeUntilDestroy()).subscribe((event: any) => {
        switch (event.settingId) {
          case 'doubleClickToFullscreen':
            this.chapterSettingSignal.update(current => ({
              ...current,
              isFullScreen: event.newValue,
            }));
            break;
          case 'nightMode':
            this.chapterSettingSignal.update(current => ({
              ...current,
              isNightMode: event.newValue,
            }));
            break;
          case 'autoNextChapter':
            this.chapterSettingSignal.update(current => ({
              ...current,
              isAutoNextChapter: event.newValue,
            }));
            break;
          case 'verticalReading':
            this.chapterSettingSignal.update(current => ({
              ...current,
              isVertical: event.newValue,
            }));
            this.changeDirectionReading(event.newValue);
            break;
          case 'zoom-reading':
            this.zoomDataSignal.update(current => ({
              ...current,
              zoomValue: event.newValue,
            }));
            break;
          case 'preloadPages':
            this.chapterSettingSignal.update(current => ({
              ...current,
              preloadPages: event.newValue,
            }));
            break;
          case 'fixedToolbar':
            this.chapterSettingSignal.update(current => ({
              ...current,
              fixedToolbar: event.newValue,
            }));
            break;
          case 'styleToolbar':
            this.chapterSettingSignal.update(current => ({
              ...current,
              toolbarStyle: event.newValue,
            }));
            break;
        }
        this.safeMarkForCheck();
      })
    );
  }

  autoChangeServerErrorModal() {
    if (!this.modal) {
      return;
    }

    if (this.intervalRef) {
      clearInterval(this.intervalRef);
      this.intervalRef = null;
    }
    this.errorImages.set(0);
    this.isErrorPages = false;
    this.countdown.set(5);
    this.modal.show();

    this.intervalRef = setInterval(() => {
      const current = this.countdown();
      this.countdown.set(current - 1);

      if (current - 1 <= 0) {
        clearInterval(this.intervalRef);
        this.intervalRef = null;
        this.modal.onSubmit();
      }
    }, 1000);
  }

  handleCancelChangeServerModal() {
    if (this.intervalRef) {
      clearInterval(this.intervalRef);
      this.intervalRef = null;
    }
    this.countdown.set(5);
    this.errorImages.set(0);
    this.isErrorPages = false;
    this.modal.hide();
  }

  loadChapterPage() {
    this.isImageLoadingSignal.set(true);
    this.addSubscription(
      this.route.data.pipe(this.takeUntilDestroy()).subscribe((routeData: any) => {
        const { response, chapterkey, redirect } = routeData.ChapterImgRes;

        if (redirect) {
          this.router.navigate(redirect);
          return;
        }


        // Initialize toolbar position if not set
        if (response === null || response.data === null) {
          this.router.navigate(['/']);
          return;
        }
        const comic : Comic = response.data?.comic;
        if(comic && !comic.coverImage?.startsWith('http://')){
          comic.coverImage = "https://cdn1.anhtruyen.com/coverimg/" + comic.coverImage;
        }
        const chapterData = structuredClone(response.data)!;
        if (this.urlService.domainType === 1 && this.responseInit && !chapterkey.includes('chuong-')) {
          this.responseInit.status = 301;
          this.responseInit.headers = {
            location: this.urlService.getChapterDetailUrl(chapterData.comic, chapterData).join('/'),
          };
          return;
        }


        this.listChapterServersSignal.set(chapterData.chapterServers);
        this.isImageLoadingSignal.set(false);
        
        this.comicSignal.set(chapterData.comic);
        this.mainChapterSignal.set(chapterData);
        this.breadcrumbLinksSignal.set([
          { label: 'Trang chủ', url: '/' },
          {
            label: this.comic()?.title || '',
            url: this.urlService.getComicDetailUrl(this.comic())
          },
          {
            label: this.mainChapter()?.title || '',
            url: this.urlService.getChapterDetailUrl(this.comic()!, this.mainChapter()!)
          }
        ]);
        this.listImgsSignal.set([this.bannerImg, ...chapterData.chapterServers[0].images]);
        this.selectedServerSignal.set(chapterData.chapterServers[0]);
        this.runInBrowser(() => {
          const comic = this.comic();
          const mainChapter = this.mainChapter();
          if (comic && mainChapter) {
            this.historyService.SaveHistory(comic, {
              id: mainChapter.id,
              title: mainChapter.title,
              slug: mainChapter.slug,
              updateAt: mainChapter.updateAt,
              viewCount: mainChapter.viewCount,
            });

            this.addSubscription(
              this.comicService
                .getChapters(comic.id)
                .pipe(this.takeUntilDestroy())
                .subscribe((res: any) => {
                  this.comicSignal.update(current => ({
                    ...current!,
                    chapters: res.data || [],
                  }));
                  this.safeMarkForCheck();
                })
            );
          }
        });

        const isErrorImage = this.listImgs()?.length === 0 && this.listChapterServers()?.length > 1;

        if (isErrorImage) {
          if (this.modal) {
            this.autoChangeServerErrorModal();
          }
        }
        this.setupSeo();
        this.isTrackingSignal.set(false);
        this.safeMarkForCheck();
      })
    );
  }

  changeServer(server: ChapterServer) {

    this.selectedServerSignal.set(server);
    if (server.id == 0) {
      this.listImgsSignal.set([this.bannerImg, ...server.images]);
      this.safeMarkForCheck();
      return;
    }
    this.isImageLoadingSignal.set(true);
    this.addSubscription(
      this.comicService
        .getChapterServer(server.id)
        .pipe(this.takeUntilDestroy())
        .subscribe((res: any) => {
          const pagesImages: string[] = res.data?.images ?? [];
          this.listImgsSignal.set([
            this.bannerImg,
            ...pagesImages]);
          this.isImageLoadingSignal.set(false);
          this.safeMarkForCheck();
        })
    );
  }
  showMoreServer() {
    this.showAllServersSignal.update(current => !current);
  }
  openSetting() {
    this.popupService.showSetting(SettingCategory.READING);
  }

  onChangeChapter(chapter: Chapter) {
    if (chapter.id === this.mainChapter()!.id) return;
    this.isImageLoadingSignal.set(true);
    if (this.chapterSetting().isFullScreen) {
      const screenElement = this.getScreenElement();
      if (screenElement) {
        screenElement.scrollTo({ top: 0 });
      }
    }
    const comic = this.comic();
    if (comic) {

      const routerLink = this.urlService.getChapterDetailUrl(comic, chapter);
      this.router.navigate(routerLink);
    }
  }

  zoomImage(zoomIn: boolean): void {
    const currentZoomData = this.zoomData();

    if (zoomIn) {
      const newZoomValue = Math.min(currentZoomData.zoomValue + 10, currentZoomData.maxZoomLevel);
      this.zoomDataSignal.update(current => ({
        ...current,
        zoomValue: newZoomValue,
      }));
    } else {
      const newZoomValue = Math.max(currentZoomData.zoomValue - 10, currentZoomData.minZoomLevel);
      this.zoomDataSignal.update(current => ({
        ...current,
        zoomValue: newZoomValue,
      }));
    }

    const updatedZoomData = this.zoomData();
    if (updatedZoomData.zoomValue <= updatedZoomData.minZoomLevel) {
      this.zoomDataSignal.update(current => ({
        ...current,
        isZoomIn: false,
      }));
      return;
    }
    if (updatedZoomData.zoomValue >= updatedZoomData.maxZoomLevel) {
      this.zoomDataSignal.update(current => ({
        ...current,
        isZoomIn: true,
      }));
      return;
    }
    this.settingService.setSettingValue('zoom-reading', updatedZoomData.zoomValue);
  }

  resetView(): void {
    const currentZoomData = this.zoomData();
    this.zoomDataSignal.update(current => ({
      ...current,
      isZoomIn: false,
      zoomValue: current.defaultZoomLevel,
    }));
    this.settingService.setSettingValue('zoom-reading', currentZoomData.defaultZoomLevel);
  }

  getZoomPercentage(): number {
    return this.zoomPercentage();
  }

  @HostListener('document:keydown.arrowleft', ['$event'])
  onNextChapter(): void {
    this.navigateChapter(false);
  }
  @HostListener('document:keydown.arrowright', ['$event'])
  onPreviousChapter(): void {
    this.navigateChapter(true);
  }


  navigateChapter = throttle((isNext: boolean): void => {
    if (this.isImageLoading()) {
      return;
    }

    const chapter = isNext ? this.getNextChapter() : this.getPreviousChapter();
    if (chapter) {
      this.onChangeChapter(chapter);
    }
  }, 1000);

  @HostListener('document:fullscreenchange', ['$event'])
  onFullscreenChange(_event: Event) {
    const isFullScreen = document.fullscreenElement != null;
    this.chapterSettingSignal.update(current => ({
      ...current,
      isFullScreen,
    }));
    this.safeMarkForCheck();
    this.stickyStateSignal.set(StickyState.NoneSticky);
    this.applyStickyState(StickyState.NoneSticky);

    const controlBarContainer = this.getControlBarContainerElement();
    const screenElement = this.getScreenElement();

    if (controlBarContainer) {
      const rect = controlBarContainer.getBoundingClientRect();

      if (isFullScreen && screenElement) {
        this.toolbarOriginalPositionSignal.set(rect.top + screenElement.scrollTop);
      } else {
        this.toolbarOriginalPositionSignal.set(rect.top + window.scrollY);
      }
    }
  }

  toggleFullscreen(): void {
    const elem = this.getScreenElement();
    if (!elem) return;

    if (!this.chapterSetting().isFullScreen) {
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if ((elem as any).mozRequestFullScreen) {
        // Firefox
        (elem as any).mozRequestFullScreen();
      } else if ((elem as any).webkitRequestFullscreen) {
        // Chrome, Safari, and Opera
        (elem as any).webkitRequestFullscreen();
      } else if ((elem as any).msRequestFullscreen) {
        // IE/Edge
        (elem as any).msRequestFullscreen();
      }
    } else {
      document.exitFullscreen();
    }
  }

  scrollToTop(event: Event): void {
    event.preventDefault();
    if (this.chapterSetting().isFullScreen) {
      const screenElement = this.getScreenElement();
      if (screenElement) {
        screenElement.scrollTo({ top: 0, behavior: 'smooth' });
      }
      return;
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  /**
   * Handle scroll events from fullscreen container
   */
  handleFullScreenScroll($event: Event) {
    const target = $event.target as HTMLElement;
    this.handleScroll(target);
  }

  /**
   * Handle scroll events from window
   */
  @HostListener('window:scroll', ['$event'])
  handleWindowScroll(_event: any) {
    this.handleScroll();
  }

  /**
   * Unified scroll handler that works for both window and fullscreen modes
   * @param scrollContainer - Optional scroll container element (for fullscreen mode)
   */
  handleScroll(scrollContainer?: HTMLElement) {
    const scrollData = this.getScrollData(scrollContainer);

    // Auto next chapter functionality
    if (this.shouldAutoNavigateToNextChapter(scrollData)) {
      this.navigateChapter(false);
      return;
    }

    // Update control bar sticky state
    this.updateStickerState(scrollData);

    // Track user engagement (view count and experience)
    this.trackUserEngagement(scrollData.scrollTop);

    // Update last scroll position for direction detection
    this.lastScrollTopSignal.set(scrollData.scrollTop <= 0 ? 0 : scrollData.scrollTop);
  }

  /**
   * Get scroll data from appropriate container (window or fullscreen element)
   */
  private getScrollData(scrollContainer?: HTMLElement) {
    // Check if we're in fullscreen mode and have a scroll container
    const isFullScreenMode = this.chapterSetting().isFullScreen && scrollContainer;

    if (isFullScreenMode) {
      // Fullscreen mode - use container scroll data
      return {
        scrollTop: scrollContainer!.scrollTop,
        scrollHeight: scrollContainer!.scrollHeight,
        clientHeight: scrollContainer!.clientHeight,
        isFullScreen: true,
      };
    } else {
      // Normal mode - use window scroll data
      return {
        scrollTop:
          window.scrollY || document.documentElement.scrollTop || document.body.scrollTop || 0,
        scrollHeight: document.documentElement.scrollHeight,
        clientHeight: window.innerHeight,
        isFullScreen: false,
      };
    }
  }

  /**
   * Check if should auto navigate to next chapter
   */
  private shouldAutoNavigateToNextChapter(scrollData: any): boolean {
    const isAtBottom =
      scrollData.scrollTop + scrollData.clientHeight >= scrollData.scrollHeight - 1;
    const chapterSetting = this.chapterSetting();
    return chapterSetting.isAutoNextChapter && chapterSetting.isVertical && isAtBottom;
  }

  /**
   * Track user engagement for view count and experience points
   */
  private trackUserEngagement(scrollOffset: number) {
    if (this.isTracking()) return;

    if (!this.isTracking() && scrollOffset > 2000) {
      this.isTrackingSignal.set(true);
      const comic = this.comic();
      const mainChapter = this.mainChapter();

      if (comic && mainChapter) {
        this.addSubscription(
          this.comicService
            .updateViewAndExp(comic.id, mainChapter.id, UserExpType.Chapter)
            .pipe(this.takeUntilDestroy())
            .subscribe(() => { })
        );
      }
    }
  }

  // Performance optimization methods
  trackByPageId = (index: number, page: string): any => {
    return index;
  };

  /**
   * Enhanced sticky state management with cleaner logic
   * @param scrollData - Scroll data from either window or fullscreen container
   */
  updateStickerState(scrollData: any) {
    const previousState = this.stickyState();

    // Determine new sticky state based on scroll position
    const newState = this.calculateStickyState(scrollData);

    // Only apply changes if state has changed
    if (previousState !== newState) {
      this.stickyStateSignal.set(newState);
      this.applyStickyState(newState);
    }
  }

  /**
   * Calculate the appropriate sticky state based on scroll position
   * @param scrollData - Scroll data containing position and dimensions
   */
  private calculateStickyState(scrollData: any): StickyState {
    const { scrollTop, clientHeight, isFullScreen } = scrollData;

    // Calculate end chapter position based on scroll mode
    const endChapterElement = this.getEndChapterElement();
    const endChapterTop = isFullScreen
      ? this.getFullscreenEndChapterPosition()
      : endChapterElement?.offsetTop || 0;

    const isEndChapter = scrollTop + clientHeight > endChapterTop;
    const isScrolledPastToolbar = scrollTop > this.toolbarOriginalPosition();

    const preState = this.scrollState();
    const newScrollState = scrollTop < this.lastScrollTop() ? 'up' : 'down';
    this.scrollStateSignal.set(newScrollState);

    if (preState !== newScrollState) {
      this.statePositionSignal.set(scrollTop);
    }

    if (!isScrolledPastToolbar) {
      return StickyState.NoneSticky;
    }

    // End of chapter - stick to bottom
    if (this.chapterSetting().fixedToolbar || isEndChapter) {
      return StickyState.Sticky;
    }

    if (newScrollState === 'up' && this.statePosition() - scrollTop > 50) {
      return StickyState.Sticky;
    }

    // Scrolling down for significant distance - hide toolbar
    if (newScrollState === 'down' && scrollTop - this.statePosition() > 200) {
      return StickyState.StickyInvisible;
    }

    // Default to current state if no change needed
    return this.stickyState();
  }

  /**
   * Get end chapter position for fullscreen mode
   */
  private getFullscreenEndChapterPosition(): number {
    const endChapterElement = this.getEndChapterElement();
    const screenElement = this.getScreenElement();

    if (!endChapterElement || !screenElement) return 0;

    // In fullscreen mode, calculate relative position within the scroll container
    const endChapterRect = endChapterElement.getBoundingClientRect();
    const containerRect = screenElement.getBoundingClientRect();

    return endChapterRect.top - containerRect.top + screenElement.scrollTop;
  }

  /**
   * Get toolbar position based on scroll mode (window vs fullscreen)
   */

  /**
   * Apply the sticky state to the control bar
   */
  private applyStickyState(state: StickyState) {
    switch (state) {
      case StickyState.Sticky:
        this.stickToTop();
        break;
      case StickyState.StickyInvisible:
        this.stickToInvisible();
        break;
      case StickyState.NoneSticky:
        this.cancelSticky();
        break;
    }
  }
  /**
   * Cancel sticky positioning and return to normal state
   */
  cancelSticky() {
    const element = this.getControlBarElement();
    if (element) {
      this.showScrollToTopSignal.set(false);
      element.classList.remove('sticky-top', 'sticky-invisible');
    }
  }

  /**
   * Stick control bar to top with enhanced animation
   */
  stickToTop() {
    const element = this.getControlBarElement();
    if (element) {
      // Apply sticky top positioning
      element.classList.remove('sticky-invisible');
      element.classList.add('sticky-top');
      this.showScrollToTopSignal.set(true);
    }
  }

  stickToInvisible() {
    const element = this.getControlBarElement();
    if (element) {
      // Apply invisible state
      element.classList.remove('sticky-top');
      element.classList.add('sticky-invisible');
      this.showScrollToTopSignal.set(false);
    }
  }

  changeDirectionReading(stage: boolean) {
    this.chapterSettingSignal.update(current => ({
      ...current,
      isVertical: stage,
    }));
    this.addMouseWheelEvent();
    const styles = stage
      ? {
        'scroll-snap-align': 'start',
        flex: '0 0 auto',
        display: 'flex',
        'flex-direction': 'column',
        'overflow-y': 'auto',
        'overflow-x': 'hidden',
      }
      : {
        'margin-top': '30px',
        'min-width': '30rem',
        'scroll-snap-align': 'start',
        display: 'flex',
        'flex-direction': 'row',
        overflow: 'hidden',
        'overflow-x': 'auto',
        'overflow-y': 'hidden',
      };

    const imageElement = this.getImageElement();
    if (imageElement) {
      for (const [key, value] of Object.entries(styles)) {
        this.renderer.setStyle(imageElement, key, value);
      }
    }
  }

  enableNightLight(stage: boolean) {
    this.chapterSettingSignal.update(current => ({
      ...current,
      isNightMode: stage,
    }));
  }

  addMouseWheelEvent() {
    const container = this.getImageElement();
    if (container) {
      container.removeEventListener('wheel', this.handleWheelEvent);
      container.addEventListener('wheel', this.handleWheelEvent);
    }
  }

  scrollHorizontal(direction: number) {
    const container = this.getImageElement();
    if (container) {
      const scrollAmount = direction * 500;
      container.scrollBy({
        left: scrollAmount,
        behavior: 'smooth',
      });
      if (this.chapterSetting().isAutoNextChapter) {
        this.checkScrollEnd();
      }
    }
  }

  handleWheelEvent = (event: WheelEvent) => {
    const container = this.getImageElement();
    if (container) {
      const scrollGap = 2.5;
      if (!this.chapterSetting().isVertical) {
        container.scrollBy({
          left: event.deltaY * scrollGap,
          behavior: 'smooth',
        });
        if (this.chapterSetting().isAutoNextChapter) {
          this.checkScrollEnd();
        }
        event.preventDefault();
      }
    }
  };

  checkScrollEnd() {
    const container = this.getImageElement();
    if (!container) return;

    if (this.chapterSetting().isVertical) {
      if (container.scrollTop + container.clientHeight >= container.scrollHeight) {
        this.navigateChapter(true);
        container.scrollTop = 0;
        container.scrollLeft = 0;
      }
    } else {
      if (container.scrollLeft + container.clientWidth >= container.scrollWidth) {
        this.navigateChapter(false);
        container.scrollTop = 0;
        container.scrollLeft = 0;
      }
    }
  }
  setupSeo() {
    const comic = this.comic();
    const mainChapter = this.mainChapter();

    if (!comic || !mainChapter) {
      return;
    }

    const chapterName = `Chương ${mainChapter.slug}`;
    const title = `Đọc ${comic.title} ${chapterName} Mới Nhất`;
    const description = this.generateChapterSEODescription();

    const seoData = {
      title,
      description,
      type: 'article' as const,
      url: this.urlService.getFullChapterUrl(comic, mainChapter),
      image: comic.coverImage,
      author: comic.author,
      publishedTime: mainChapter.updateAt,
      modifiedTime: mainChapter.updateAt,
      section: 'Chương truyện',
      tags: comic.genres?.map(g => g.title) || [],
      siteName: 'MeTruyenMoi',
      canonical: this.urlService.getFullChapterCanonicalUrl(comic, mainChapter),
      twitterCard: 'summary_large_image' as const,
    };

    this.seoService.setSEOData(seoData);

    // Add chapter structured data
    const chapterSchema = this.seoService.generateChapterSchema(comic, mainChapter);

    // Add breadcrumb structured data
    const breadcrumbSchema = this.seoService.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl },
      { name: comic.title, url: this.urlService.getFullComicUrl(comic) },
      { name: chapterName, url: this.urlService.getFullChapterUrl(comic, mainChapter) },
    ]);

    // Add organization schema
    const organizationSchema = this.seoService.generateOrganizationSchema();

    // Combine all schemas
    const combinedSchema = [chapterSchema, breadcrumbSchema, organizationSchema];
    this.seoService.addStructuredData(combinedSchema);
  }

  private generateChapterSEODescription(): string {
    const comic = this.comic();
    const mainChapter = this.mainChapter();

    if (!comic || !mainChapter) {
      return '';
    }

    const chapterName = mainChapter.title || `Chapter ${mainChapter.slug}`;
    const baseDescription = `Đọc ${comic.title} ${chapterName} online miễn phí tại MeTruyenMoi`;
    const totalChapters = comic.numChapter;

    return `${baseDescription}. Tổng ${totalChapters} chương. Cập nhật nhanh, chất lượng HD.`;
  }

  // private generateReadingProgressSchema(): any {
  //   const currentChapterIndex = this.comic.chapters?.findIndex(
  //     chapter => chapter.id === this.mainChapter.id
  //   ) || 0;
  //   const totalChapters = this.comic.numChapter || 1;
  //   const progressPercentage = Math.round(((currentChapterIndex + 1) / totalChapters) * 100);
  //   const chapterName = this.mainChapter.title || `Chương ${this.mainChapter.slug}`;

  //   return {
  //     '@context': 'https://schema.org',
  //     '@type': 'ReadAction',
  //     'object': {
  //       '@type': 'Book',
  //       'name': this.comic.title,
  //       'url': `${this.urlService.baseUrl}/truyen-tranh/${this.comic.url}-${this.comic.id}`,
  //       'author': this.seoService.generateAuthorObject(this.comic.author),
  //       'genre': this.comic.genres?.map((g: any) => g.title || g.name) || [],
  //       'numberOfPages': totalChapters
  //     },
  //     'actionStatus': 'ActiveActionStatus',
  //     'startTime': new Date().toISOString(),
  //     'endTime': new Date(Date.now() + 15 * 60 * 1000).toISOString(), // Estimated 30 min reading time
  //     'location': {
  //       '@type': 'Place',
  //       'name': 'MeTruyenMoi',
  //       'url': this.urlService.baseUrl
  //     },
  //     'result': {
  //       '@type': 'Chapter',
  //       'name': chapterName,
  //       'position': currentChapterIndex + 1,
  //       'url': `${this.urlService.baseUrl}/truyen-tranh/${this.comic.url}/${this.mainChapter.id}`,
  //       'isPartOf': {
  //         '@type': 'Book',
  //         'name': this.comic.title,
  //         'url': `${this.urlService.baseUrl}/truyen-tranh/${this.comic.url}-${this.comic.id}`
  //       },
  //       'pageStart': 1,
  //       'pageEnd': this.mainChapter.pages?.length || 0,
  //       'inLanguage': 'vi'
  //     },
  //     'agent': {
  //       '@type': 'Person',
  //       'name': 'Comic Reader'
  //     },
  //     'additionalProperty': [
  //       {
  //         '@type': 'PropertyValue',
  //         'name': 'readingProgress',
  //         'value': `${progressPercentage}%`
  //       },
  //       {
  //         '@type': 'PropertyValue',
  //         'name': 'totalChapters',
  //         'value': totalChapters
  //       },
  //       {
  //         '@type': 'PropertyValue',
  //         'name': 'currentChapter',
  //         'value': currentChapterIndex + 1
  //       },
  //       {
  //         '@type': 'PropertyValue',
  //         'name': 'pagesInChapter',
  //         'value': this.mainChapter.pages?.length || 0
  //       }
  //     ]
  //   };
  // }

  handleSubmitChangeServerModal() {
    clearInterval(this.intervalRef);
    const servers = this.listChapterServers();
    const current = this.selectedServer();
    const currentIndex = servers.indexOf(current);
    if (currentIndex + 1 >= servers.length) {
      return;
    }
    const nextServer = servers[(currentIndex + 1)];
    if (nextServer && nextServer !== current) {
      this.changeServer(nextServer);
    }
  }

  reportError() {
    const comic = this.comic();
    const mainChapter = this.mainChapter();

    if (comic && mainChapter) {
      this.popupService.showReportComic({
        comicID: comic.id,
        chapterID: mainChapter.id,
      });
    }
  }

  onLoad($event: Event) {
    const imageEle = $event.target as HTMLImageElement;
    imageEle.onload = null;
    imageEle.classList.remove('hidden');
  }
  onError($event: ErrorEvent) {
    const imageEle = $event.target as HTMLImageElement;
    imageEle.onerror = null;
    imageEle.classList.add('hidden');

    this.errorImages.set(this.errorImages() + 1);
    const errorRatio = this.errorImages() / (this.listImgs()?.length || 1);

    if (
      !this.isErrorPages &&
      errorRatio > 0.5 && // Tỷ lệ lỗi ảnh >= 50%
      this.listChapterServers()?.length > 1
    ) {
      this.isErrorPages = true;
      this.autoChangeServerErrorModal();
    }
  }
}
