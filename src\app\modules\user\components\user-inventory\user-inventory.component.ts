import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  Inject,
  OnInit,
  PLATFORM_ID,
  signal
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { ToastService } from '@services/toast.service';
import {
  EquipmentSlotType,
  InventoryFilter,
  InventorySortType,
  ITEM_CATEGORY_INFO,
  ITEM_RARITY_INFO,
  ItemActionType,
  ItemCategory,
  ItemDisplayInfo,
  ItemRarity,
  UserInventoryItem
} from '../../interfaces/item.interface';
import { ItemService } from '../../services/item.service';
import { ItemCardComponent } from '../item-card/item-card.component';

type InventoryTab = 'owned' | 'equipped' | 'all';

@Component({
  selector: 'app-user-inventory',
  standalone: true,
  imports: [CommonModule, FormsModule, ItemCardComponent],
  templateUrl: './user-inventory.component.html',
  styleUrl: './user-inventory.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserInventoryComponent extends OptimizedBaseComponent implements OnInit {
  // Signals for reactive state
  private readonly inventoryItemsSignal = signal<UserInventoryItem[]>([]);
  private readonly equippedItemsSignal = signal<any[]>([]);
  private readonly allItemTemplatesSignal = signal<any[]>([]);
  private readonly isLoadingSignal = signal<boolean>(false);
  private readonly currentPageSignal = signal<number>(1);
  private readonly totalPagesSignal = signal<number>(1);
  private readonly filterSignal = signal<InventoryFilter>({});
  private readonly selectedItemsSignal = signal<Set<number>>(new Set());
  private readonly cardSizeSignal = signal<'small' | 'medium' | 'large'>('medium');
  private readonly activeTabSignal = signal<InventoryTab>('owned');

  // Computed properties
  readonly inventoryItems = computed(() => this.inventoryItemsSignal());
  readonly equippedItems = computed(() => this.equippedItemsSignal());
  readonly allItemTemplates = computed(() => this.allItemTemplatesSignal());
  readonly isLoading = computed(() => this.isLoadingSignal());
  readonly currentPage = computed(() => this.currentPageSignal());
  readonly totalPages = computed(() => this.totalPagesSignal());
  readonly filter = computed(() => this.filterSignal());
  readonly selectedItems = computed(() => this.selectedItemsSignal());
  readonly cardSize = computed(() => this.cardSizeSignal());
  readonly activeTab = computed(() => this.activeTabSignal());

  // Constants for templates
  readonly ItemCategory = ItemCategory;
  readonly ItemRarity = ItemRarity;
  readonly ItemActionType = ItemActionType;
  readonly ITEM_CATEGORY_INFO = ITEM_CATEGORY_INFO;
  readonly ITEM_RARITY_INFO = ITEM_RARITY_INFO;

  // Tab info
  readonly tabInfo = {
    owned: {
      title: 'Đang sở hữu',
      icon: 'icon-backpack',
      description: 'Vật phẩm bạn đang sở hữu'
    },
    equipped: {
      title: 'Đang trang bị',
      icon: 'icon-equipped',
      description: 'Vật phẩm bạn đang trang bị'
    },
    all: {
      title: 'Toàn bộ items',
      icon: 'icon-catalog',
      description: 'Tất cả vật phẩm trong hệ thống'
    }
  };

  // Filter and sort options
  readonly categoryOptions = Object.values(ItemCategory);
  readonly rarityOptions = Object.values(ItemRarity);
  readonly sortOptions = [
    { value: InventorySortType.NAME, label: 'Tên' },
    { value: InventorySortType.RARITY, label: 'Độ hiếm' },
    { value: InventorySortType.QUANTITY, label: 'Số lượng' },
    { value: InventorySortType.OBTAINED_DATE, label: 'Ngày nhận' },
    { value: InventorySortType.CATEGORY, label: 'Loại' }
  ];

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private itemService: ItemService,
    private toastService: ToastService
  ) {
    super(cdr, platformId);
  }

  ngOnInit(): void {
    this.runInBrowser(() => {
      this.toastService.info('Tính năng <b>Kho đồ</b> đang được phát triển, vui lòng thử lại sau!');
      this.loadInventory();
      this.setupSubscriptions();
    });
  }

  // Computed getters
  get displayItems(): ItemDisplayInfo[] {
    const currentTab = this.activeTab();

    switch (currentTab) {
      case 'owned':
        return this.inventoryItems().map(item => this.mapToDisplayInfo(item));

      case 'equipped':
        return this.equippedItems().map(item => this.mapEquippedToDisplayInfo(item));

      case 'all':
        return this.allItemTemplates().map(template => this.mapTemplateToDisplayInfo(template));

      default:
        return this.inventoryItems().map(item => this.mapToDisplayInfo(item));
    }
  }

  get filteredItems(): ItemDisplayInfo[] {
    let items = this.displayItems;
    const currentFilter = this.filter();

    // Apply category filter
    if (currentFilter.category) {
      items = items.filter(item => item.template.category === currentFilter.category);
    }

    // Apply rarity filter
    if (currentFilter.rarity) {
      items = items.filter(item => item.template.rarity === currentFilter.rarity);
    }

    // Apply search filter
    if (currentFilter.searchTerm) {
      const searchTerm = currentFilter.searchTerm.toLowerCase();
      items = items.filter(item =>
        item.template.name.toLowerCase().includes(searchTerm) ||
        item.template.description?.toLowerCase().includes(searchTerm)
      );
    }

    // Apply sorting
    if (currentFilter.sortBy) {
      items = this.sortItems(items, currentFilter.sortBy, currentFilter.sortOrder || 'asc');
    }

    return items;
  }

  get hasSelectedItems(): boolean {
    return this.selectedItems().size > 0;
  }

  get selectedItemsCount(): number {
    return this.selectedItems().size;
  }

  // Event handlers
  onItemAction(event: { action: ItemActionType; item: ItemDisplayInfo }): void {
    const { action, item } = event;

    switch (action) {
      case ItemActionType.USE:
        this.useItem(item);
        break;
      case ItemActionType.EQUIP:
        this.equipItem(item);
        break;
      case ItemActionType.UNEQUIP:
        this.unequipItem(item);
        break;
      case ItemActionType.VIEW_DETAILS:
        this.viewItemDetails(item);
        break;
    }
  }

  onItemClick(item: ItemDisplayInfo): void {
    this.viewItemDetails(item);
  }

  onSelectionChanged(event: { item: ItemDisplayInfo; selected: boolean }): void {
    const newSelected = new Set(this.selectedItems());
    
    if (event.selected) {
      newSelected.add(event.item.template.id);
    } else {
      newSelected.delete(event.item.template.id);
    }
    
    this.selectedItemsSignal.set(newSelected);
  }

  onFilterChange(newFilter: Partial<InventoryFilter>): void {
    this.filterSignal.set({ ...this.filter(), ...newFilter });
    this.currentPageSignal.set(1);
    this.loadInventory();
  }

  onPageChange(page: number): void {
    this.currentPageSignal.set(page);
    this.loadInventory();
  }


  onTabChange(tab: InventoryTab): void {
    this.activeTabSignal.set(tab);
    this.clearSelection();
    this.currentPageSignal.set(1);

    // Load data based on tab
    switch (tab) {
      case 'owned':
        this.loadInventory();
        break;
      case 'equipped':
        this.loadEquippedItems();
        break;
      case 'all':
        this.loadAllItemTemplates();
        break;
    }
  }

  clearSelection(): void {
    this.selectedItemsSignal.set(new Set());
  }

  // Actions
  private async useItem(item: ItemDisplayInfo): Promise<void> {
    if (!item.canUse || !item.quantity) return;

    this.isLoadingSignal.set(true);

    try {
      const result = await this.itemService.useItem(item.template.id, 1).toPromise();

      if (result?.success) {
        // Service will automatically refresh inventory and currency
        console.log('Item used successfully:', result.message);
      } else {
        console.error('Failed to use item:', result?.message);
      }
    } catch (error) {
      console.error('Error using item:', error);
    } finally {
      this.isLoadingSignal.set(false);
    }
  }

  private async equipItem(item: ItemDisplayInfo): Promise<void> {
    if (!item.canEquip) return;

    const slotType = this.getSlotTypeFromCategory(item.template.category);
    if (!slotType) return;

    this.isLoadingSignal.set(true);

    try {
      const result = await this.itemService.equipItem(item.template.id, slotType).toPromise();

      if (result?.success) {
        // Service will automatically refresh equipped items
        console.log('Item equipped successfully:', result.message);
      } else {
        console.error('Failed to equip item:', result?.message);
      }
    } catch (error) {
      console.error('Error equipping item:', error);
    } finally {
      this.isLoadingSignal.set(false);
    }
  }

  private async unequipItem(item: ItemDisplayInfo): Promise<void> {
    const slotType = this.getSlotTypeFromCategory(item.template.category);
    if (!slotType) return;

    this.isLoadingSignal.set(true);

    try {
      const result = await this.itemService.unequipItem(slotType).toPromise();

      if (result?.success) {
        // Service will automatically refresh equipped items
        console.log('Item unequipped successfully:', result.message);
      } else {
        console.error('Failed to unequip item:', result?.message);
      }
    } catch (error) {
      console.error('Error unequipping item:', error);
    } finally {
      this.isLoadingSignal.set(false);
    }
  }

  private viewItemDetails(item: ItemDisplayInfo): void {
    // Open item details modal/popup
    console.log('View item details:', item);
  }

  // Data loading
  private loadInventory(): void {
    this.isLoadingSignal.set(true);

    this.addSubscription(
      this.itemService.getUserInventory(this.currentPage(), 20)
        .subscribe({
          next: (inventory) => {
            this.inventoryItemsSignal.set(inventory.items);
            this.totalPagesSignal.set(inventory.totalPages);
            this.isLoadingSignal.set(false);
            this.safeMarkForCheck();
          },
          error: (error) => {
            console.error('Error loading inventory:', error);
            this.isLoadingSignal.set(false);
            this.safeMarkForCheck();
          }
        })
    );
  }

  private loadEquippedItems(): void {
    this.addSubscription(
      this.itemService.getUserEquippedItems()
        .subscribe({
          next: (equippedItems) => {
            this.equippedItemsSignal.set(equippedItems);
            this.safeMarkForCheck();
          },
          error: (error) => {
            console.error('Error loading equipped items:', error);
          }
        })
    );
  }

  private loadAllItemTemplates(): void {
    this.isLoadingSignal.set(true);

    this.addSubscription(
      this.itemService.getItemTemplates()
        .subscribe({
          next: (response: any) => {
            this.allItemTemplatesSignal.set(response.items || response);
            this.totalPagesSignal.set(response.totalPages || 1);
            this.isLoadingSignal.set(false);
            this.safeMarkForCheck();
          },
          error: (error: any) => {
            console.error('Error loading all item templates:', error);
            this.isLoadingSignal.set(false);
            this.safeMarkForCheck();
          }
        })
    );
  }

  private setupSubscriptions(): void {
    // Subscribe to inventory updates
    this.addSubscription(
      this.itemService.inventory$.subscribe(items => {
        this.inventoryItemsSignal.set(items);
        this.safeMarkForCheck();
      })
    );

    // Subscribe to equipped items updates
    this.addSubscription(
      this.itemService.equippedItems$.subscribe(items => {
        this.equippedItemsSignal.set(items);
        this.safeMarkForCheck();
      })
    );

    // Subscribe to currency updates
    this.addSubscription(
      this.itemService.currency$.subscribe(currency => {
        // Handle currency updates if needed
        this.safeMarkForCheck();
      })
    );
  }

  // Utility methods
  private mapToDisplayInfo(item: UserInventoryItem): ItemDisplayInfo {
    const isEquipped = this.equippedItems().some(
      equipped => equipped.itemTemplateId === item.itemTemplateId
    );

    return {
      template: item.itemTemplate,
      quantity: item.quantity,
      isEquipped,
      canUse: this.canUseItem(item),
      canEquip: this.canEquipItem(item),
      expiresAt: item.expiresAt
    };
  }

  private mapEquippedToDisplayInfo(equippedItem: any): ItemDisplayInfo {
    return {
      template: equippedItem.itemTemplate || equippedItem,
      quantity: 1,
      isEquipped: true,
      canUse: false,
      canEquip: false,
      expiresAt: equippedItem.expiresAt
    };
  }

  private mapTemplateToDisplayInfo(template: any): ItemDisplayInfo {
    const userItem = this.inventoryItems().find(item => item.itemTemplateId === template.id);
    const isEquipped = this.equippedItems().some(equipped => equipped.itemTemplateId === template.id);

    return {
      template: template,
      quantity: userItem?.quantity || 0,
      isEquipped,
      canUse: userItem ? this.canUseItem(userItem) : false,
      canEquip: userItem ? this.canEquipItem(userItem) : false,
      expiresAt: userItem?.expiresAt
    };
  }

  private canUseItem(item: UserInventoryItem): boolean {
    return item.itemTemplate.category === ItemCategory.CONSUMABLE && 
           item.quantity > 0 && 
           (!item.expiresAt || new Date(item.expiresAt) > new Date());
  }

  private canEquipItem(item: UserInventoryItem): boolean {
    const equippableCategories = [
      ItemCategory.AVATAR_FRAME,
      ItemCategory.TITLE,
      ItemCategory.BADGE,
      ItemCategory.EQUIPMENT
    ];
    
    return equippableCategories.includes(item.itemTemplate.category) &&
           (!item.expiresAt || new Date(item.expiresAt) > new Date());
  }

  private getSlotTypeFromCategory(category: ItemCategory): EquipmentSlotType | null {
    switch (category) {
      case ItemCategory.AVATAR_FRAME:
        return EquipmentSlotType.AVATAR_FRAME;
      case ItemCategory.TITLE:
        return EquipmentSlotType.TITLE;
      case ItemCategory.BADGE:
        return EquipmentSlotType.BADGE;
      default:
        return null;
    }
  }

  private sortItems(items: ItemDisplayInfo[], sortBy: InventorySortType, order: 'asc' | 'desc'): ItemDisplayInfo[] {
    return items.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case InventorySortType.NAME:
          comparison = a.template.name.localeCompare(b.template.name);
          break;
        case InventorySortType.RARITY:
          const rarityOrder = ['common', 'uncommon', 'rare', 'epic', 'legendary'];
          comparison = rarityOrder.indexOf(a.template.rarity) - rarityOrder.indexOf(b.template.rarity);
          break;
        case InventorySortType.QUANTITY:
          comparison = (a.quantity || 0) - (b.quantity || 0);
          break;
        case InventorySortType.CATEGORY:
          comparison = a.template.category.localeCompare(b.template.category);
          break;
      }

      return order === 'desc' ? -comparison : comparison;
    });
  }

  // TrackBy functions
  trackByItemId = (index: number, item: ItemDisplayInfo): number => {
    return item.template.id;
  };
}
