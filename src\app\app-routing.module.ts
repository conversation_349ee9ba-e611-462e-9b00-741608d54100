import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ChapterImgsResolver, ComicResolver } from './resolvers/comic.resolver';

const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./modules/home/<USER>').then(m => m.HomePageComponent)
  },
  {
    path: 'truyen-tranh/:slug',
    loadComponent: () => import('./modules/comic-detail/comic-detail.component').then(m => m.ComicDetailComponent),
    resolve: { comicRes: ComicResolver },
  },
  {
    path: 'tim-truyen',
    loadChildren: () => import('./modules/search-page/search.module').then((m) => m.SearchModule,),
  },
  {
    path: 'xep-hang',
    loadChildren: () => import('./modules/rank/rank.module').then((m) => m.RankModule),
  },
  {
    path: 'lich-su',
    loadChildren: () => import('./modules/history-page/history-page.module').then((m) => m.HistoryPageModule),
  },
  {
    path: 'theo-doi',
    loadChildren: () => import('./modules/followed-page/followed-page.module').then((m) => m.FollowedPageModule,),
  },
  {
    path: 'truyen-hot',
    loadChildren: () => import('./modules/comic-hot/comic-hot.module').then((m) => m.ComicHotModule,),
  },
  {
    path: 'truyen-tranh/:comickey/:chapterkey',
    loadComponent: () => import('./modules/chapter-page/chapter-page.component').then(m => m.ChapterPageComponent),
    resolve: { ChapterImgRes: ChapterImgsResolver }
  },
  // {
  //   path: 'truyen-tranh/:comicslug/:chapterslug/:chapterid',
  //   loadChildren: () => import('./modules/chapter-page/chapter.module').then((m) => m.ChapterModule,),
  // },
  {
    path: 'tai-khoan',
    loadChildren: () => import('./modules/user/user.module').then((m) => m.UserModule),
  },
  {
    path: 'auth',
    loadChildren: () => import('./modules/authentication/auth.module').then((m) => m.AuthModule,),
  },
  {
    path: 'chinh-sach-bao-mat',
    loadChildren: () => import('./modules/privacy-policy/privacy-policy.module').then((m) => m.PrivacyPolicyModule),
  },
  {
    path: 'dieu-khoan',
    loadChildren: () => import('./modules/clause/clause.module').then((m) => m.ClauseModule),
  },
  {
    path: 'lien-he',
    loadChildren: () => import('./modules/contact/contact.module').then((m) => m.ContactModule),
  },
  {
    path: 'gioi-thieu',
    loadChildren: () => import('./modules/about/about.module').then((m) => m.AboutModule),
  },
  {
    path: 'cau-hoi-thuong-gap',
    loadChildren: () => import('./modules/faq/faq.module').then((m) => m.FaqModule),
  },
  {
    path: 'ban-quyen',
    loadChildren: () => import('./modules/dmca/dmca.module').then((m) => m.DmcaModule),
  },
  {
    path: 'the-loai',
    loadComponent: () => import('./modules/genre-index/genre-index.component').then((m) => m.GenreIndexComponent),
  },
  {
    path: 'so-do-website',
    loadComponent: () => import('./modules/html-sitemap/html-sitemap.component').then((m) => m.HtmlSitemapComponent),
  },
  {
    path: 'dong-bo-truyen',
    loadChildren: () => import('./modules/sync-tracking/sync-tracking.module').then((m) => m.SyncTrackingModule),
  },
  {
    path: 'tro-ly-ai',
    loadChildren: () => import('./modules/ai-assistant/ai-assistant.module').then((m) => m.AiAssistantModule),
  },
  {
    path: 'the-loai/:slug',
    loadChildren: () => import('./modules/genre/genre.module').then((m) => m.GenreModule),
  },
  // {
  //   path: 'tac-gia/:author',
  //   loadChildren: () => import('./modules/author-comics/author-comics.module').then((m) => m.AuthorComicsModule),
  // },
  {
    path: '**',
    redirectTo: '404',
    pathMatch: 'full'
  },
  {
    path: '404',
    loadChildren: () => import('./modules/not-found/not-found.module').then((m) => m.NotFoundModule)
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule { }
