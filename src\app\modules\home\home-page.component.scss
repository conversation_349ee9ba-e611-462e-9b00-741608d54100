
.home-content {
  @apply md:container w-full mx-auto;
}

.carousel-landing {
  @apply mt-0 mb-0 flex w-full h-full pb-48 xs:pb-56 sm:pb-60 lg:pb-[22%] relative;
}


// SEO Content Container with height animation
.seo-content-container {
  height: 400px; // Initial collapsed height
  overflow: hidden;
  transition: max-height 0.5s ease-in-out;
  
  &.expanded {
    height: 100%; // Large enough to fit all content
  }
}

.simple-carousel-wrapper {
  --sc-gap: 10px;
  --sc-items: 2;
}

@media (max-width: 1100px) {
  .simple-carousel-wrapper {
    --sc-gap: 0px;

    --sc-items: 1;
  }
}

// @media (max-width: 768px) {
//   .simple-carousel-wrapper {
//       --sc-gap: 0px;
//       --sc-items: 1;
//   }
// }