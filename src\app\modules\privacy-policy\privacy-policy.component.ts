import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { SEOData, SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';

@Component({
    selector: 'main[app-privacy-policy]',
    templateUrl: './privacy-policy.component.html',
    styleUrl: './privacy-policy.component.scss',
    standalone: false,
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class PrivacyPolicyComponent implements OnInit {
    host = '';
    siteName = 'MeTruyenMoi';
    currentDate = '';

    constructor(
        private urlService: UrlService,
        private seoService: SeoService
    ) {
        this.host = this.urlService.baseUrl;
        this.currentDate = '15/8/2025';
    }

    ngOnInit(): void {
        this.setupSEO();
    }

    setupSEO(): void {
        // Use the new comprehensive SEO method
        this.seoService.setPrivacyPolicySEO();
    }

    // Scroll to section method for table of contents
    scrollToSection(sectionId: string): void {
        const element = document.getElementById(sectionId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
}
