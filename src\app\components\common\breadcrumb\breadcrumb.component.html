<!-- Modern Breadcrumb Component - Comic Website Design -->
<nav class="breadcrumb-container"
     [class.breadcrumb-transparent]="style === 'transparent'"
     [class.breadcrumb-default]="style === 'default'"
     aria-label="Breadcrumb navigation">
  <!-- Breadcrumb Header -->
  <div class="breadcrumb-header">

    <!-- Breadcrumb List -->
    <ol class="breadcrumb-list" itemscope itemtype="https://schema.org/BreadcrumbList">
      <li
        *ngFor="let link of Links; let i = index; trackBy: trackByLink"
        class="breadcrumb-item"
        [class.breadcrumb-item-current]="i === Links.length - 1"
        itemprop="itemListElement"
        itemscope
        itemtype="https://schema.org/ListItem"
      >
        <!-- Breadcrumb Link -->
        <a
          *ngIf="i < Links.length - 1; else currentPage"
          class="breadcrumb-link"
          [routerLink]="link.url"
          [title]="link.label"
          itemprop="item"
        >
          <span class="breadcrumb-text" itemprop="name">{{ link.label }}</span>
        </a>

        <!-- Current Page (non-clickable) -->
        <ng-template #currentPage>
          <a [routerLink]="link.url" class="breadcrumb-link">
            <span class="breadcrumb-current" itemprop="name">
              {{ link.label }}
            </span>
          </a>
        </ng-template>

        <!-- Schema.org position -->
        <meta itemprop="position" [content]="i + 1">

        <!-- Divider -->
        <div *ngIf="i < Links.length - 1" class="breadcrumb-divider">
          <svg class="divider-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polyline points="9,18 15,12 9,6"/>
          </svg>
        </div>
      </li>
    </ol>
  </div>
</nav>
