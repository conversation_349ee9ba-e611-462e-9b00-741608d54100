import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { RouterLink } from '@angular/router';
import { InViewportDirective } from '@directives/inviewport.directive';
import { SocialService } from '@services/social.service';

@Component({
  selector: 'footer[app-footer]',
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [CommonModule, RouterLink, InViewportDirective],
  host: {
    class: 'relative bg-white dark:bg-dark-bg text-neutral-900 dark:text-light-text overflow-hidden'
  }
})
export class FooterComponent {
  // currentYear = new Date().getUTCFullYear();
  get isBrowser() {
    return isPlatformBrowser(this.platformId);
  }

  constructor(private _facebookService: SocialService, @Inject(PLATFORM_ID) private platformId: object) {
    // The FacebookService will load the SDK automatically
  }


  loadFacebookSDK() {
    this._facebookService.initSocialSharing();
  }

}
