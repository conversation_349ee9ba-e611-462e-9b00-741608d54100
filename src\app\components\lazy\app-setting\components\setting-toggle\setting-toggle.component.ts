import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, computed, signal } from '@angular/core';
import { SettingComponentProps } from '../../interfaces/setting-interfaces';

@Component({
  selector: '[app-setting-toggle]',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="setting-toggle-container">
      <div class="setting-toggle-header">
        <!-- <label class="setting-toggle-label" [for]="setting.id">
          {{ setting.name }}
        </label> -->
        <div class="setting-toggle-wrapper">
          <input
            type="checkbox"
            [id]="setting.id"
            [checked]="value"
            [disabled]="disabled || readonly"
            (change)="onToggleChange($event)"
            class="setting-toggle-input"
          />
          <span class="setting-toggle-slider"></span>
        </div>
      </div>

    </div>
  `,
  styleUrls: ['./setting-toggle.component.scss']
})
export class SettingToggleComponent implements SettingComponentProps {
  @Input() setting!: any;
  @Input() value: boolean = false;
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() showDescription: boolean = true;
  @Input() compact: boolean = false;
  
  @Output() onChange = new EventEmitter<boolean>();
  @Output() onValidate = new EventEmitter<any>();

  onToggleChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onChange.emit(target.checked);
  }
}
