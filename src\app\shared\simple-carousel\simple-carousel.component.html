<div
  #stage
  preventClickOnDrag
  class="sc-stage"
  [class.transitioning]="isTransitioning() && !isDragging()"
  [style.transform]="transform()"
  (pointerdown)="onDragStart($event)"
  (pointermove)="onDragMove($event)"
  (pointerup)="onDragEnd($event)"
  (pointerleave)="onDragEnd($event)"
  (touchstart)="onDragStart($event)"
  (touchmove)="onDragMove($event)"
  (transitionend)="onTransitionEnd()"
>
  <!-- Original slides -->
  <div
    class="sc-item"
    *ngFor="let item of displayItems(); let i = index; trackBy: trackByIndex"
    [attr.role]="'group'"
    [attr.aria-roledescription]="'slide'"
    [attr.aria-label]="i + 1 + ' / ' + displayItems.length"
  >
    <ng-template
      [ngTemplateOutlet]="itemTemplate || null"
      [ngTemplateOutletContext]="{ $implicit: item, index: i }"
    ></ng-template>
  </div>
</div>
<button class="mobile-nav-left" (click)="prev()" aria-label="Previous mobile carousel item">
  <svg class="mobile-nav-icon" viewBox="0 0 512 512" fill="currentColor">
    <polygon points="352,128.4 319.7,96 160,256 160,256 160,256 319.7,416 352,383.6 224.7,256" />
  </svg>
</button>
<button class="mobile-nav-right" (click)="next()" aria-label="Next mobile carousel item">
  <svg class="mobile-nav-icon" viewBox="0 0 512 512" fill="currentColor">
    <polygon points="160,128.4 192.3,96 352,256 352,256 352,256 192.3,416 160,383.6 287.3,256" />
  </svg>
</button>

<ng-content></ng-content>
