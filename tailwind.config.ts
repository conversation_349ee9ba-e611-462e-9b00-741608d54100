/** @type {import('tailwindcss').Config} */
import config from './globalConfig';
module.exports = {
  prefix: '',
  mode: 'jit',
  darkMode: 'class',
  content: ['./src/**/*.{html,ts}' ],
  plugins: [require('@tailwindcss/container-queries')],

  theme: {
    screens: { ...config.screens },
    extend: {
      colors: {
        'light-text': '#e5e7eb',
        'dark-text': '#000',
        'light-bg': '#fff',
        'dark-bg': '#191A1C',
        // primary: {
        //   50: '#FFB3A0',
        //   100: '#E83A3A',
        //   200: '#D1202A',
        //   300: '#7A0000',
        //   400: '#660000',
        //   500: '#5F0000',
        // },
        primary: {
          50: '#FFC49B',
          100: '#F86E4C',
          200: '#D04C2E',
          300: '#A92811',
          400: '#820000',
          500: '#5F0000',
        },
        secondary: {
          100: '#050505',
        },
      },

    },
  },
};
