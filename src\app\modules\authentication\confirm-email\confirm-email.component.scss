// Import shared authentication styles
@use '../login-form/login-form.component.scss';

// Email Confirmation Specific Styles
.confirmation-content {
  @apply text-center py-4;
}

.confirmation-icon {
  @apply text-6xl mb-6;
  animation: emailPulse 2s infinite;
}

.confirmation-title {
  @apply text-2xl font-bold text-neutral-900 dark:text-light-text mb-4;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.confirmation-message {
  @apply text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed mb-8;
}

// Instructions
.confirmation-instructions {
  @apply space-y-4 mb-8 text-left;
}

.instruction-item {
  @apply flex items-start gap-3;
}

.instruction-number {
  @apply w-6 h-6 bg-primary-100 text-white text-xs font-bold;
  @apply rounded-full flex items-center justify-center flex-shrink-0 mt-0.5;
}

.instruction-text {
  @apply text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed;
}

// Resend Section
.resend-section {
  @apply mb-8;
}

.resend-text {
  @apply text-sm text-neutral-600 dark:text-neutral-400 mb-4;
}

.resend-button {
  @apply w-full bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300;
  @apply font-semibold py-3 px-6 rounded-xl;
  @apply hover:bg-neutral-200 dark:hover:bg-neutral-700;
  @apply focus:outline-none focus:ring-2 focus:ring-neutral-300 focus:ring-offset-2;
  @apply transition-all duration-200 transform hover:scale-105;
  @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;

  &:disabled {
    @apply bg-neutral-50 dark:bg-neutral-900 text-neutral-400 dark:text-neutral-600;
  }
}

// Animations
@keyframes emailPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}