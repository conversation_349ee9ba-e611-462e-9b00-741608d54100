// Import shared authentication styles
@use '../login-form/login-form.component.scss';

// Success State Styles
.success-state {
  @apply text-center py-8;
}

.success-icon {
  @apply text-6xl mb-6;
  animation: successBounce 0.6s ease-out;
}

.success-title {
  @apply text-2xl font-bold text-neutral-900 dark:text-light-text mb-4;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.success-message {
  @apply text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed mb-8;
}

.success-actions {
  @apply flex justify-center;
}

.back-to-login {
  @apply inline-flex items-center gap-2 px-6 py-3;
  @apply bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300;
  @apply rounded-xl font-medium transition-all duration-200;
  @apply hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:scale-105;
}

// Animations
@keyframes successBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}