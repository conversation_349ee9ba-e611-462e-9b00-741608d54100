

.equipped-items-container {
  @apply space-y-6;
}

// Header
.equipped-header {
  @apply text-center space-y-2;
}

.equipped-title {
  @apply text-xl font-bold text-neutral-900 dark:text-neutral-100;
}

.equipped-subtitle {
  @apply text-neutral-600 dark:text-neutral-400;
}

// Loading
.loading-container {
  @apply flex flex-col items-center justify-center py-8 space-y-4;
  @apply text-neutral-500 dark:text-neutral-400;
}

.loading-spinner {
  @apply w-6 h-6 border-2 border-neutral-300 border-t-sky-500 rounded-full animate-spin;
}

// Equipment Slots
.equipment-slots {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6;
}

.equipment-slot {
  @apply bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700;
  @apply p-4 transition-all duration-200 hover:shadow-md cursor-pointer;

  &--empty {
    @apply border-dashed border-neutral-300 dark:border-neutral-600;
    @apply hover:border-sky-400 dark:hover:border-sky-500;
  }
}

// Slot Header
.slot-header {
  @apply flex items-center gap-3 mb-4;
}

.slot-icon {
  @apply w-10 h-10 bg-neutral-100 dark:bg-neutral-700 rounded-lg;
  @apply flex items-center justify-center text-neutral-600 dark:text-neutral-400;
}

.slot-info {
  @apply flex-1 min-w-0;
}

.slot-label {
  @apply font-semibold text-neutral-900 dark:text-neutral-100 text-sm;
}

.slot-description {
  @apply text-xs text-neutral-600 dark:text-neutral-400 truncate;
}

// Slot Content
.slot-content {
  @apply min-h-[120px] flex items-center justify-center;
}

.equipped-item {
  @apply w-full;
}

// Empty Slot
.empty-slot {
  @apply text-center space-y-3 py-4;
}

.empty-slot-icon {
  @apply text-3xl text-neutral-300 dark:text-neutral-600;
}

.empty-slot-text {
  @apply text-sm text-neutral-500 dark:text-neutral-400;
}

.empty-slot-btn {
  @apply px-3 py-1.5 bg-sky-500 text-white text-sm rounded-md;
  @apply hover:bg-sky-600 transition-colors;
}

// Empty State
.empty-state {
  @apply text-center py-12 space-y-4;
}

.empty-state__icon {
  @apply text-6xl text-neutral-300 dark:text-neutral-600;
}

.empty-state__title {
  @apply text-xl font-semibold text-neutral-900 dark:text-neutral-100;
}

.empty-state__description {
  @apply text-neutral-600 dark:text-neutral-400 max-w-md mx-auto;
}

.empty-state__btn {
  @apply px-6 py-3 bg-sky-500 text-white rounded-lg;
  @apply hover:bg-sky-600 transition-colors font-medium;
}

// Icon styles
[class^="icon-"], [class*=" icon-"] {
  @apply inline-block w-5 h-5;
}

// Responsive adjustments
@screen sm {
  .equipment-slots {
    @apply grid-cols-2;
  }
}

@screen lg {
  .equipment-slots {
    @apply grid-cols-4;
  }
  
  .equipped-title {
    @apply text-2xl;
  }
}

// Dark mode specific adjustments
@media (prefers-color-scheme: dark) {
  .equipment-slot {
    &--empty {
      @apply bg-neutral-900/50;
    }
  }
}
