import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Meta, MetaDefinition, Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Chapter, Comic, Genre } from '@schema';
import { UrlService } from './url.service';

export interface SEOData {
  title: string;
  description: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'book' | 'video';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  locale?: string;
  siteName?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  keywords?: string;
  category?: string;
}

@Injectable({
  providedIn: 'root',
})
export class SeoService {
  private readonly siteName = 'MeTruyenMoi';
  private readonly defaultImage = '/logo.png';
  private readonly themeColor = '#fff';

  constructor(
    private title: Title,
    private meta: Meta,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    private urlService: UrlService,
    @Inject(PLATFORM_ID) private platformId: object
  ) {
  }

  /**
   * Clear all dynamic meta tags before setting new ones
   */
  private clearDynamicMetaTags(): void {
    // Remove dynamic meta tags that should be updated per page
    const dynamicTags = [
      'description', 'author', 'robots', 'keywords', 'category',
      'og:title', 'og:description', 'og:type', 'og:url', 'og:image', 'og:site_name', 'og:locale',
      'twitter:title', 'twitter:description', 'twitter:url', 'twitter:image', 'twitter:card',
      'article:published_time', 'article:modified_time', 'article:section', 'article:tag'
    ];

    dynamicTags.forEach(tag => {
      if (tag.startsWith('og:') || tag.startsWith('twitter:') || tag.startsWith('article:')) {
        this.meta.removeTag(`property="${tag}"`);
      } else {
        this.meta.removeTag(`name="${tag}"`);
      }
    });
  }

  /**
   * Generate long-tail keyword links for a comic and chapter
   */
  genComicKeyWords(comic: Comic, chapter: Chapter): { title: string; href: string }[] {
    const comicUrl = this.urlService.getFullComicUrl(comic);
    const chapterUrl = this.urlService.getFullChapterUrl(comic, chapter);

    return [
      { title: `Truyện ${comic.title}`, href: comicUrl },
      { title: `Đọc truyện ${comic.title} online`, href: comicUrl },
      { title: `${comic.title} chapter mới nhất`, href: chapterUrl },
      { title: `Truyện tranh ${comic.title}`, href: comicUrl },
      { title: `${comic.title} truyện tranh`, href: comicUrl },
      { title: `${comic.title} manga`, href: comicUrl },
    ];
  }

  /**
   * Set comprehensive SEO data for a page
   */
  setSEOData(data: SEOData): void {
    // Clear existing dynamic meta tags first
    this.clearDynamicMetaTags();

    // Set title
    const fullTitle = data.title ? `${data.title} | ${this.siteName}` : this.siteName;
    this.title.setTitle(fullTitle);

    // Get current URL
    const currentUrl = data.url || this.urlService.getUrl(this.router.url);

    // Basic meta tags
    this.updateTag({ name: 'description', content: data.description });
    this.updateTag({ name: 'author', content: data.author || this.siteName });

    // Keywords meta tag
    const keywords = data.keywords || this.getDefaultKeywords();
    this.updateTag({ name: 'keywords', content: keywords });

    // Category meta tag
    if (data.category) {
      this.updateTag({ name: 'category', content: data.category });
    }

    // Robots meta
    const robotsContent = this.getRobotsContent(data.noindex, data.nofollow);
    this.updateTag({ name: 'robots', content: robotsContent });

    // Open Graph tags
    this.updateTag({ property: 'og:title', content: data.title || this.siteName });
    this.updateTag({ property: 'og:description', content: data.description });
    this.updateTag({ property: 'og:type', content: data.type || 'website' });
    this.updateTag({ property: 'og:url', content: currentUrl });
    this.updateTag({ property: 'og:image', content: data.image || this.urlService.getUrl(this.defaultImage) });
    this.updateTag({ property: 'og:site_name', content: data.siteName || this.siteName });
    this.updateTag({ property: 'og:locale', content: data.locale || 'vi_VN' });

    // Article specific tags
    if (data.type === 'article' || data.type === 'book') {
      if (data.publishedTime) {
        this.updateTag({ property: 'article:published_time', content: data.publishedTime });
      }
      if (data.modifiedTime) {
        this.updateTag({ property: 'article:modified_time', content: data.modifiedTime });
      }
      if (data.section) {
        this.updateTag({ property: 'article:section', content: data.section });
      }
      if (data.tags) {
        data.tags.forEach(tag => {
          this.addTag({ property: 'article:tag', content: tag });
        });
      }
    }

    // Twitter Card tags
    this.updateTag({ name: 'twitter:card', content: data.twitterCard || 'summary_large_image' });
    this.updateTag({ name: 'twitter:title', content: data.title || this.siteName });
    this.updateTag({ name: 'twitter:description', content: data.description });
    this.updateTag({ name: 'twitter:image', content: data.image || this.urlService.getUrl(this.defaultImage) });

    // Canonical URL
    this.updateCanonical(data.canonical || currentUrl);

    // Additional meta tags for better SEO (using consistent theme color)
    this.updateTag({ name: 'theme-color', content: this.themeColor });
    this.updateTag({ name: 'msapplication-TileColor', content: this.themeColor });
  }

  /**
   * Add structured data (JSON-LD)
   */
  addStructuredData(schema: any): void {
    if (isPlatformBrowser(this.platformId)) {
      // Remove existing structured data
      this.removeAllStructuredData();

      // Handle both single schema and array of schemas
      const schemas = Array.isArray(schema) ? schema : [schema];

      schemas.forEach((singleSchema, index) => {
        const script = this.document.createElement('script');
        script.type = 'application/ld+json';
        script.text = JSON.stringify(singleSchema, null, 0); // Minified JSON
        script.id = `structured-data-${index}`;
        script.setAttribute('data-schema-type', singleSchema['@type'] || 'Unknown');
        this.document.head.appendChild(script);
      });
    }
  }

  /**
   * Remove all existing structured data
   */
  private removeAllStructuredData(): void {
    // Remove all structured data scripts
    const existingScripts = this.document.querySelectorAll('script[type="application/ld+json"]');
    existingScripts.forEach(script => {
      if (script.id.startsWith('structured-data-') || script.id === 'structured-data') {
        script.remove();
      }
    });
  }

  /**
   * Set page title
   */
  setTitle(title: string): void {
    const fullTitle = title ? `${title} | ${this.siteName}` : this.siteName;
    this.title.setTitle(fullTitle);
  }

  /**
   * Add meta tags
   */
  addTags(tags: MetaDefinition[]): void {
    this.meta.addTags(tags);
  }

  /**
   * Add single meta tag
   */
  addTag(tag: MetaDefinition): void {
    this.meta.addTag(tag);
  }

  /**
   * Update meta tag
   */
  updateTag(tag: MetaDefinition): void {
    this.meta.updateTag(tag);
  }

  /**
   * Update canonical link
   */
  updateCanonical(url: string): void {
    this.updateLink('canonical', url);
  }

  /**
   * Update link element
   */
  updateLink(rel: string, href: string): void {
    const head = this.document.head;
    let element: HTMLLinkElement | null = this.document.querySelector(`link[rel='${rel}']`);

    if (!element) {
      element = this.document.createElement('link') as HTMLLinkElement;
      head.appendChild(element);
    }

    element.setAttribute('rel', rel);
    element.setAttribute('href', href);
  }

  /**
   * Get default keywords with enhanced long-tail keywords
   */
  private getDefaultKeywords(): string {
    return 'truyện tranh, manga, comic, đọc truyện online, truyện tranh hay, manga việt nam, comic việt nam, truyện tranh miễn phí, đọc truyện tranh không quảng cáo, website đọc truyện tốt nhất, truyện tranh cập nhật nhanh nhất, đọc manga full hd chất lượng cao, truyện tranh online miễn phí Việt Nam';
  }

  /**
   * Generate long-tail keywords for better SEO targeting
   */
  private generateLongTailKeywords(baseKeyword: string, type: 'genre' | 'comic' | 'chapter' | 'search' = 'genre'): string[] {
    const longTailPatterns = {
      genre: [
        `đọc truyện tranh ${baseKeyword} online miễn phí`,
        `truyện tranh ${baseKeyword} hay nhất việt nam`,
        `list truyện ${baseKeyword} đáng đọc nhất`,
        `truyện tranh ${baseKeyword} full bộ`,
        `tìm truyện ${baseKeyword} chất lượng cao`,
        `top truyện tranh ${baseKeyword} hay nhất`,
        `${baseKeyword} truyện tranh không lag`,
        `web đọc truyện ${baseKeyword} tốt nhất`
      ],
      comic: [
        `đọc ${baseKeyword} chapter mới nhất`,
        `${baseKeyword} full bộ tiếng việt`,
        `truyện tranh ${baseKeyword} online`,
        `${baseKeyword} manga scan việt`,
        `${baseKeyword} không quảng cáo`,
        `${baseKeyword} chất lượng hd`,
        `tải ${baseKeyword} về máy`,
        `${baseKeyword} update nhanh`,
        `${baseKeyword} dịch thuật chất lượng`,
        `${baseKeyword} đọc miễn phí`
      ],
      chapter: [
        `đọc ${baseKeyword} chapter mới`,
        `${baseKeyword} chương mới nhất`,
        `${baseKeyword} full chapter`,
        `${baseKeyword} không lag`,
        `${baseKeyword} hd quality`,
        `${baseKeyword} tiếng việt`,
        `${baseKeyword} scan chất lượng`,
        `${baseKeyword} online free`
      ],
      search: [
        `tìm truyện tranh ${baseKeyword}`,
        `search manga ${baseKeyword}`,
        `${baseKeyword} truyện hay`,
        `${baseKeyword} manhwa recommend`,
        `${baseKeyword} top rated`,
        `${baseKeyword} full series`,
        `${baseKeyword} completed manga`,
        `${baseKeyword} ongoing series`
      ]
    };

    return longTailPatterns[type] || longTailPatterns.genre;
  }

  /**
   * Set SEO for genre/category page with enhanced long-tail keywords
   */
  setGenreSEO(genre: Genre, comics?: Comic[], page: number = 1): void {
    const title = page > 1
      ? `Truyện tranh ${genre.title} - Trang ${page}`
      : `Truyện tranh ${genre.title}`;

    // Enhanced description with long-tail keywords
    const description = page > 1
      ? `Trang ${page} - Đọc truyện tranh thể loại ${genre.title} online miễn phí. ${comics?.length || 0} bộ truyện chất lượng cao HD, cập nhật nhanh nhất tại ${this.siteName}. Khám phá manga ${genre.title} hay nhất Việt Nam.`
      : `Đọc truyện tranh thể loại ${genre.title} online miễn phí. ${comics?.length || 0} bộ truyện chất lượng cao HD, cập nhật liên tục tại ${this.siteName}. Top manga ${genre.title} hay nhất, full bộ tiếng Việt không quảng cáo.`;

    // Generate comprehensive long-tail keywords
    const longTailKeywords = this.generateLongTailKeywords(genre.title, 'genre');
    const keywords = [
      genre.title,
      `truyện tranh ${genre.title}`,
      ...longTailKeywords.slice(0, 5), // Take top 5 long-tail keywords
      ...this.getDefaultKeywords().split(', ')
    ].join(', ');

    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords,
      url: this.urlService.getUrl(`/the-loai/${genre.slug}`),
      category: genre.title,
      canonical: this.urlService.getCanonicalUrl(`/the-loai/${genre.slug}`)
    };

    this.setSEOData(seoData);

    // Add structured data
    const schemas = [
      this.generateGenreSchema(genre, comics || []),
      this.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: this.urlService.baseUrl },
        { name: `Thể loại ${genre.title}`, url: this.urlService.getUrl(`/the-loai/${genre.slug}`) }
      ]),
    ];

    this.addStructuredData(schemas);
  }

  /**
   * Generate genre page structured data
   */
  private generateGenreSchema(genre: Genre, comics: Comic[]): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      'name': `Truyện tranh ${genre.title}`,
      'description': genre.description || `Kho tàng truyện tranh thể loại ${genre.title}`,
      'url': this.urlService.getUrl(`/the-loai/${genre.slug}`),
      'mainEntity': {
      '@type': 'ItemList',
      'name': `Danh sách truyện ${genre.title}`,
      'numberOfItems': comics.length,
      'itemListElement': comics.slice(0, 10).map((comic, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'item': {
        '@type': 'Book',
        'name': comic.title,
        'url': this.urlService.getFullComicUrl(comic),
        'image': comic.coverImage || undefined,
        'author': comic.author || undefined,
        'genre': genre.title
        }
      }))
      },
      'breadcrumb': {
      '@type': 'BreadcrumbList',
      'itemListElement': [
        {
        '@type': 'ListItem',
        'position': 1,
        'name': 'Trang chủ',
          'item': this.urlService.getUrl('/')
        },
        {
        '@type': 'ListItem',
        'position': 2,
        'name': `Thể loại ${genre.title}`,
        'item': this.urlService.getUrl(`/the-loai/${genre.slug}`)
        }
      ]
      }
    };
  }

  /**
   * Generate consistent author object for E-A-T signals
   */
  generateAuthorObject(authorName?: string): any {
    if (authorName && authorName.trim()) {
      return {
        '@type': 'Person',
        'name': authorName.trim()
      };
    }
    return undefined;
    // Use organization as author for better E-A-T signals when no author is available
  }

  /**
   * Set SEO for search page with enhanced long-tail keywords
   */
  setSearchSEO(query?: string, totalResults?: number, page: number = 1): void {
    const searchQuery = query?.trim() || '';
    const pageText = page > 1 ? ` - Trang ${page}` : '';
    
    const title = searchQuery 
      ? `Tìm kiếm "${searchQuery}"${pageText}`
      : `Tìm kiếm truyện tranh${pageText}`;
      
    const description = searchQuery
      ? `Kết quả tìm kiếm cho "${searchQuery}". Tìm thấy ${totalResults || 0} truyện tranh chất lượng cao. Đọc ngay các bộ manga, manhwa, manhua "${searchQuery}" hay nhất tại ${this.siteName} - cập nhật nhanh, full HD, miễn phí.`
      : `Tìm kiếm trong kho tàng hơn 10,000+ bộ truyện tranh manga, manhwa, manhua chất lượng cao. Tìm truyện yêu thích với công cụ tìm kiếm thông minh tại ${this.siteName} - web đọc truyện tốt nhất Việt Nam.`;
      
    // Generate search-specific long-tail keywords
    const searchLongTailKeywords = searchQuery 
      ? this.generateLongTailKeywords(searchQuery, 'search')
      : [
          'tìm kiếm truyện tranh online',
          'search manga việt nam',
          'tìm truyện hay nhất',
          'công cụ tìm kiếm truyện',
          'filter truyện tranh',
          'tìm manga theo thể loại',
          'search manhwa completed',
          'tìm truyện full bộ'
        ];

    const keywords = searchQuery 
      ? [
          searchQuery,
          `tìm kiếm ${searchQuery}`,
          `truyện tranh ${searchQuery}`,
          `manga ${searchQuery}`,
          ...searchLongTailKeywords.slice(0, 6),
          ...this.getDefaultKeywords().split(', ')
        ].join(', ')
      : [
          'tìm kiếm truyện tranh',
          'search manga',
          'tìm truyện online',
          ...searchLongTailKeywords,
          ...this.getDefaultKeywords().split(', ')
        ].join(', ');

    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords,
      url: this.urlService.getUrl(`/tim-truyen?q=${encodeURIComponent(searchQuery)}&page=${page}`),
      canonical: this.urlService.getCanonicalUrl('/tim-truyen')
    };

    this.setSEOData(seoData);
    
    // Add search schema
    if (searchQuery) {
      const searchSchema = this.generateSearchSchema(searchQuery, totalResults || 0);
      const breadcrumbSchema = this.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: this.urlService.getUrl('/') },
        { name: 'Tìm kiếm', url: this.urlService.getUrl('/tim-truyen') }
      ]);
      this.addStructuredData([searchSchema, breadcrumbSchema]);
    }
  }

  /**
   * Set SEO for ranking page with enhanced long-tail keywords
   */
  setRankingSEO(listComics: Comic[], page: number = 1): void {
    const pageText = page > 1 ? ` - Trang ${page}` : '';
    const title = `Bảng Xếp Hạng Truyện Tranh Hay Nhất${pageText}`;
    const description = page > 1 
      ? `Trang ${page} - Top truyện tranh được yêu thích nhất, xếp hạng theo lượt đọc và đánh giá từ độc giả. Khám phá những bộ manga, manhwa, manhua đỉnh cao nhất tại ${this.siteName}.`
      : `Top truyện tranh được yêu thích nhất, xếp hạng theo lượt đọc và đánh giá từ độc giả Việt Nam. Khám phá những bộ manga, manhwa, manhua hot nhất và đáng đọc nhất hiện tại tại ${this.siteName}.`;
    
    const longTailKeywords = [
      'top truyện tranh hay nhất việt nam',
      'bảng xếp hạng manga 2025',
      'truyện tranh được đọc nhiều nhất',
      'top manhwa rating cao',
      'xếp hạng truyện theo lượt view',
      'truyện tranh popular nhất',
      'top rated manga vietnam',
      'best manga manhwa manhua'
    ];

    const keywords = [
      'xếp hạng truyện tranh',
      'top truyện hay',
      'bảng xếp hạng manga',
      'truyện tranh popular',
      'top rated comics',
      ...longTailKeywords,
      ...this.getDefaultKeywords().split(', ')
    ].join(', ');
    
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords,
      canonical: this.urlService.getCanonicalUrl('/xep-hang')
    };

    this.setSEOData(seoData);
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.getUrl('/') },
      { name: 'Xếp hạng', url: this.urlService.getUrl('/xep-hang') }
    ]);
    const structuredData = [breadcrumbSchema]
    if (page === 1 && listComics && listComics.length > 0) {
      const itemListSchema = this.generateComicListSchema(
        listComics, // Show top 20 for better performance
        'Truyện Tranh Mới Nhất',
        'Danh sách truyện tranh mới cập nhật hàng ngày tại MeTruyenMoi'
      );
      structuredData.push(itemListSchema);
    }
    this.addStructuredData(structuredData);
  }

  /**
   * Set SEO for hot comics page with enhanced long-tail keywords
   */
  setHotComicsSEO(listComics: Comic[], page: number = 1): void {
    const pageText = page > 1 ? ` - Trang ${page}` : '';
    const title = `Truyện Tranh Hot - Trending Manga Manhwa${pageText}`;
    const description = page > 1
      ? `Trang ${page} - Tuyển tập những bộ truyện tranh hot nhất, trending mạnh nhất hiện tại. Đọc ngay các bộ manga, manhwa, manhua được yêu thích nhất tại ${this.siteName}.`
      : `Tuyển tập những bộ truyện tranh hot nhất, trending mạnh, được đọc nhiều nhất hiện tại. Cập nhật liên tục các bộ manga, manhwa, manhua viral và đỉnh cao nhất tại ${this.siteName}.`;
    
    const longTailKeywords = [
      'truyện tranh trending 2025',
      'manga hot nhất hiện tại',
      'manhwa viral việt nam',
      'truyện tranh được yêu thích nhất',
      'top truyện hot trong tuần',
      'manga trending today',
      'comic hot nhất tháng',
      'truyện viral trên mạng'
    ];

    const keywords = [
      'truyện hot',
      'truyện tranh hot',
      'truyện trending',
      'manga viral',
      'manhwa popular',
      'comic hot nhất',
      ...longTailKeywords,
      ...this.getDefaultKeywords().split(', ')
    ].join(', ');
    
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords,
      canonical: this.urlService.getCanonicalUrl('/truyen-hot')
    };

    this.setSEOData(seoData);
    
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.getUrl('/') },
      { name: 'Truyện hot', url: this.urlService.getUrl('/truyen-hot') }
    ]);
    const structuredData = [breadcrumbSchema]
    if (page === 1 && listComics && listComics.length > 0) {
      const itemListSchema = this.generateComicListSchema(
        listComics.slice(0, 20), // Show top 20 for better performance
        'Truyện Tranh Hot',
        'Danh sách truyện tranh hot nhất, trending mạnh nhất hiện tại tại MeTruyenMoi'
      );
      structuredData.push(itemListSchema);
    }
    this.addStructuredData(structuredData);
  }

  /**
   * Set SEO for history page
   */
  setHistorySEO(): void {
    const title = `Lịch sử đọc truyện`;
    const description = `Xem lại lịch sử các bộ truyện tranh bạn đã đọc. Tiếp tục đọc từ chương cuối cùng một cách dễ dàng tại ${this.siteName}.`;
    
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `lịch sử đọc truyện, đọc tiếp truyện, ${this.getDefaultKeywords()}`,
      canonical: this.urlService.getCanonicalUrl('/lich-su'),
      noindex: true // Private user data
    };

    this.setSEOData(seoData);
    
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.getUrl('/') },
      { name: 'Lịch sử', url: this.urlService.getUrl('/lich-su') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for followed page
   */
  setFollowedSEO(): void {
    const title = `Truyện đã theo dõi`;
    const description = `Quản lý danh sách các bộ truyện tranh bạn đang theo dõi. Nhận thông báo khi có chương mới được cập nhật tại ${this.siteName}.`;
    
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `theo dõi truyện, bookmark truyện, ${this.getDefaultKeywords()}`,
      canonical: this.urlService.getCanonicalUrl('/theo-doi'),
      noindex: true // Private user data
    };

    this.setSEOData(seoData);
    
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.getUrl('/') },
      { name: 'Theo dõi', url: this.urlService.getUrl('/theo-doi') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for contact page
   */
  setContactSEO(): void {
    const title = `Liên hệ`;
    const description = `Liên hệ với đội ngũ ${this.siteName}. Gửi góp ý, báo lỗi hoặc đề xuất cải thiện để chúng tôi phục vụ bạn tốt hơn.`;
    
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `liên hệ, góp ý, báo lỗi, hỗ trợ, ${this.siteName}`,
      canonical: this.urlService.getCanonicalUrl('/lien-he')
    };

    this.setSEOData(seoData);
    
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.getUrl('/') },
      { name: 'Liên hệ', url: this.urlService.getUrl('/lien-he') }
    ]);
    
    const contactSchema = this.generateContactPageSchema();
    this.addStructuredData([breadcrumbSchema, contactSchema]);
  }

  /**
   * Set SEO for About page
   */
  setAboutSEO(): void {
    const title = `Giới thiệu`;
    const description = `Giới thiệu về ${this.siteName} - nền tảng đọc truyện tranh miễn phí, tốc độ nhanh, giao diện thân thiện và cập nhật liên tục.`;
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `giới thiệu, về chúng tôi, ${this.siteName}, đọc truyện nhanh, không quảng cáo`,
      canonical: this.urlService.getCanonicalUrl('/gioi-thieu')
    };
    this.setSEOData(seoData);
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.getUrl('/') },
      { name: 'Giới thiệu', url: this.urlService.getUrl('/gioi-thieu') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for FAQ page
   */
  setFaqSEO(): void {
    const title = `Câu hỏi thường gặp (FAQ)`;
    const description = `Giải đáp các câu hỏi thường gặp khi sử dụng ${this.siteName}: cách tìm truyện, theo dõi, đồng bộ và báo lỗi.`;
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `faq, câu hỏi thường gặp, hướng dẫn sử dụng, ${this.siteName}`,
      canonical: this.urlService.getCanonicalUrl('/cau-hoi-thuong-gap')
    };
    this.setSEOData(seoData);
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.getUrl('/') },
      { name: 'FAQ', url: this.urlService.getUrl('/cau-hoi-thuong-gap') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for DMCA/Copyright page
   */
  setDmcaSEO(): void {
    const title = `Chính sách bản quyền (DMCA)`;
    const description = `Chính sách DMCA và bản quyền của ${this.siteName}. Hướng dẫn báo cáo vi phạm và quy trình xử lý nội dung.`;
    const seoData: SEOData = {
      title,
      description,
      type: 'article',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `DMCA, bản quyền, khiếu nại, gỡ nội dung, ${this.siteName}`,
      canonical: this.urlService.getCanonicalUrl('/ban-quyen')
    };
    this.setSEOData(seoData);
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl },
      { name: 'Bản quyền', url: this.urlService.getUrl('/ban-quyen') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for Genre Index page
   */
  setGenreIndexSEO(): void {
    const title = `Danh sách thể loại truyện tranh`;
    const description = `Tổng hợp tất cả thể loại truyện tranh trên ${this.siteName}: hành động, lãng mạn, phiêu lưu, kinh dị, học đường...`;
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `danh sách thể loại, thể loại truyện tranh, manga genres, ${this.siteName}`,
      canonical: this.urlService.getCanonicalUrl('/the-loai')
    };
    this.setSEOData(seoData);
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl },
      { name: 'Thể loại', url: this.urlService.getUrl('/the-loai') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for HTML sitemap page
   */
  setHtmlSitemapSEO(): void {
    const title = `Sơ đồ website (HTML Sitemap)`;
    const description = `Sơ đồ website của ${this.siteName}. Dễ dàng truy cập tất cả trang quan trọng: trang chủ, thể loại, xếp hạng, truyện hot, liên hệ...`;
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `sitemap, sơ đồ website, html sitemap, điều hướng, ${this.siteName}`,
      canonical: this.urlService.getCanonicalUrl('/so-do-website')
    };
    this.setSEOData(seoData);
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl },
      { name: 'Sơ đồ website', url: this.urlService.getUrl('/so-do-website') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for privacy policy page
   */
  setPrivacyPolicySEO(): void {
    const title = `Chính sách bảo mật`;
    const description = `Chính sách bảo mật thông tin người dùng của ${this.siteName}. Cam kết bảo vệ quyền riêng tư và an toàn thông tin cá nhân.`;
    
    const seoData: SEOData = {
      title,
      description,
      type: 'article',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `chính sách bảo mật, quyền riêng tư, bảo vệ thông tin, ${this.siteName}`,
      canonical: this.urlService.getCanonicalUrl('/chinh-sach-bao-mat')
    };

    this.setSEOData(seoData);
    
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.getUrl('/') },
      { name: 'Chính sách bảo mật', url: this.urlService.getUrl('/chinh-sach-bao-mat') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for terms page
   */
  setTermsSEO(): void {
    const title = `Điều khoản sử dụng`;
    const description = `Điều khoản và quy định sử dụng dịch vụ tại ${this.siteName}. Vui lòng đọc kỹ trước khi sử dụng website.`;
    
    const seoData: SEOData = {
      title,
      description,
      type: 'article',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `điều khoản sử dụng, quy định, điều lệ, ${this.siteName}`,
      canonical: this.urlService.getCanonicalUrl('/dieu-khoan')
    };

    this.setSEOData(seoData);
    
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl },
      { name: 'Điều khoản', url: this.urlService.getUrl('/dieu-khoan') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for AI assistant page
   */
  setAIAssistantSEO(): void {
    const title = `Trợ lý AI`;
    const description = `Trợ lý AI thông minh giúp bạn tìm kiếm và gợi ý những bộ truyện tranh phù hợp. Khám phá truyện mới với công nghệ AI tại ${this.siteName}.`;
    
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `trợ lý AI, gợi ý truyện, tìm truyện AI, ${this.getDefaultKeywords()}`,
      canonical: this.urlService.getCanonicalUrl('/tro-ly-ai')
    };

    this.setSEOData(seoData);
    
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl },
      { name: 'Trợ lý AI', url: this.urlService.getUrl('/tro-ly-ai') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for chapter reading page with enhanced long-tail keywords
   */
  setChapterSEO(comic: Comic, chapter: Chapter): void {
    const chapterTitle = `Chương ${chapter.slug}`;
    const title = `${comic.title} ${chapterTitle} - Đọc Online Miễn Phí`;
    
    const description = `Đọc ${comic.title} ${chapterTitle} online miễn phí chất lượng HD tại ${this.siteName}. Cập nhật nhanh nhất, không quảng cáo, đọc mượt mà trên mọi thiết bị. Tiếp tục theo dõi câu chuyện hấp dẫn!`;
    
    // Generate chapter-specific long-tail keywords
    const chapterLongTailKeywords = this.generateLongTailKeywords(comic.title, 'chapter');
    
    const keywords = [
      `${comic.title} ${chapterTitle}`,
      `đọc ${comic.title} chapter ${chapter.slug}`,
      `${comic.title} chương mới nhất`,
      `${comic.title} online free`,
      `${comic.title} no ads`,
      ...chapterLongTailKeywords.slice(0, 6),
      ...this.getDefaultKeywords().split(', ')
    ];

    if (comic.genres) {
      keywords.push(...comic.genres.slice(0, 3).map(g => g.title));
    }

    const seoData: SEOData = {
      title,
      description,
      type: 'article',
      siteName: this.siteName,
      twitterCard: 'summary_large_image',
      keywords: keywords.join(', '),
      image: comic.coverImage || this.urlService.getUrl(this.defaultImage),
      url: this.urlService.getFullChapterUrl(comic, chapter),
      canonical: this.urlService.getCanonicalUrl(`/truyen-tranh/${comic.url}/${chapter.slug}`),
      publishedTime: chapter.updateAt,
      modifiedTime: chapter.updateAt,
      section: comic.genres?.[0]?.title,
      author: comic.author
    };

    this.setSEOData(seoData);

    // Add structured data
    const chapterSchema = this.generateChapterSchema(comic, chapter);
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl },
      { name: comic.title, url: this.urlService.getFullComicUrl(comic) },
      { name: chapterTitle, url: this.urlService.getFullChapterUrl(comic, chapter) }
    ]);

    this.addStructuredData([chapterSchema, breadcrumbSchema]);
  }

  /**
   * Set SEO for sync tracking page
   */
  setSyncTrackingSEO(): void {
    const title = `Đồng bộ dữ liệu đọc truyện`;
    const description = `Đồng bộ lịch sử đọc truyện và danh sách theo dõi từ các website khác. Chuyển đổi dữ liệu dễ dàng tại ${this.siteName}.`;
    
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `đồng bộ truyện, chuyển đổi dữ liệu, import dữ liệu đọc truyện, ${this.siteName}`,
      canonical: this.urlService.getCanonicalUrl('/dong-bo-truyen')
    };

    this.setSEOData(seoData);
    
    const breadcrumbSchema = this.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl },
      { name: 'Đồng bộ truyện', url: this.urlService.getUrl('/dong-bo-truyen') }
    ]);
    this.addStructuredData([breadcrumbSchema]);
  }

  /**
   * Set SEO for not found page
   */
  setNotFoundSEO(): void {
    const title = `Không tìm thấy trang`;
    const description = `Trang bạn tìm kiếm không tồn tại hoặc đã bị di chuyển. Quay về trang chủ để khám phá hàng ngàn bộ truyện tranh hay tại ${this.siteName}.`;
    
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: this.getDefaultKeywords(),
      canonical: this.urlService.getCanonicalUrl('/404'),
      noindex: true
    };

    this.setSEOData(seoData);
  }

  /**
   * Set SEO for authentication pages
   */
  setAuthSEO(type: 'login' | 'register' | 'forgot-password' | 'confirm-email'): void {
    const titles = {
      login: 'Đăng nhập',
      register: 'Đăng ký',
      'forgot-password': 'Quên mật khẩu',
      'confirm-email': 'Xác nhận email'
    };
    
    const descriptions = {
      login: 'Đăng nhập để theo dõi truyện yêu thích và đồng bộ lịch sử đọc',
      register: 'Đăng ký tài khoản miễn phí để trải nghiệm đầy đủ tính năng',
      'forgot-password': 'Khôi phục mật khẩu tài khoản của bạn',
      'confirm-email': 'Xác nhận địa chỉ email để kích hoạt tài khoản'
    };
    
    const title = `${titles[type]}`;
    const description = `${descriptions[type]} tại ${this.siteName}.`;
    
    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords: `${titles[type]}, tài khoản, ${this.siteName}`,
      canonical: this.urlService.getCanonicalUrl('/auth'),
      noindex: true
    };

    this.setSEOData(seoData);
  }

  /**
   * Generate search result schema
   */
  private generateSearchSchema(query: string, totalResults: number): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'SearchResultsPage',
      'mainEntity': {
        '@type': 'ItemList',
        'name': `Kết quả tìm kiếm cho "${query}"`,
        'numberOfItems': totalResults
      },
      'breadcrumb': {
        '@type': 'BreadcrumbList',
        'itemListElement': [
          {
            '@type': 'ListItem',
            'position': 1,
            'name': 'Trang chủ',
            'item': this.urlService.baseUrl
          },
          {
            '@type': 'ListItem',
            'position': 2,
            'name': 'Tìm kiếm',
            'item': this.urlService.getUrl('/tim-truyen')
          }
        ]
      }
    };
  }

  /**
   * Generate contact page schema
   */
  private generateContactPageSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'ContactPage',
      'name': 'Liên hệ',
      'description': `Trang liên hệ của ${this.siteName}`,
      'url': this.urlService.getUrl('/lien-he'),
      'mainEntity': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.urlService.getUrl('/'),
        'contactPoint': {
          '@type': 'ContactPoint',
          'contactType': 'Customer Service',
          'availableLanguage': 'Vietnamese'
        }
      }
    };
  }

  /**
   * Get robots content
   */
  private getRobotsContent(noindex?: boolean, nofollow?: boolean): string {
    const index = noindex ? 'noindex' : 'index';
    const follow = nofollow ? 'nofollow' : 'follow';
    return `${index}, ${follow}`;
  }

  /**
   * Generate breadcrumb structured data
   */
  generateBreadcrumbSchema(breadcrumbs: Array<{ name: string, url: string }>): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': item.name,
        'item': item.url
      }))
    };
  }

  /**
   * Generate website structured data
   */
  generateWebsiteSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'name': this.siteName,
      'url': this.urlService.baseUrl,
      'description': 'Website đọc truyện tranh online miễn phí với kho tàng truyện tranh phong phú, cập nhật liên tục',
      'potentialAction': {
        '@type': 'SearchAction',
        'target': `${this.urlService.baseUrl}/tim-kiem?keyword={search_term_string}`,
        'query-input': 'required name=search_term_string'
      },
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.urlService.baseUrl,
        'logo': {
          '@type': 'ImageObject',
          'url': `${this.urlService.baseUrl}/logo.png`,
          'width': 136,
          'height': 64
        }
      }
    };
  }

  /**
   * Generate comic book structured data optimized for manga/comic websites
   */
  generateComicSchema(comic: Comic): any {
    const schema: any = {
      '@context': 'https://schema.org',
      '@type': 'Book',
      'name': comic.title,
      'alternateName': comic.otherName || undefined,
      'description': comic.description || `Đọc truyện ${comic.title} online miễn phí tại ${this.siteName}`,
      'image': comic.coverImage || this.urlService.getUrl(this.defaultImage),
      'url': this.urlService.getFullComicUrl(comic),
      'identifier': {
        '@type': 'PropertyValue',
        'name': 'Comic ID',
        'value': comic.id.toString()
      },
      'author': this.generateAuthorObject(comic.author),
      'genre': comic.genres?.map((g) => g.title) || [],
      "bookFormat": "https://schema.org/GraphicNovel",
      'inLanguage': 'vi',
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.urlService.baseUrl
      },
      'datePublished': comic.createAt || comic.updateAt,
      'dateModified': comic.updateAt,
      'numberOfChapters': comic.numChapter,
      'workExample': comic.chapters?.slice(0, 5).map((chapter: Chapter) => ({
        '@type': 'Chapter',
        'name': chapter.title || `Chương ${chapter.slug}`,
        'url': `${this.urlService.getFullChapterUrl(comic, chapter)}`,
        'position': chapter.slug || chapter.id
      })) || [],
      'audience': {
        '@type': 'Audience',
        'audienceType': 'Comic Readers'
      },
      'contentRating': this.getContentRating(comic.genres),
      'keywords': this.generateComicKeywords(comic)
    };

    return schema;
  }

  /**
   * Generate content rating based on genres
   */
  private getContentRating(genres: Genre[]): string {
    if (!genres) return 'General';
    const genreNames = genres.map(g => (g.title).toLowerCase());
    if (genreNames.some(name => ['adult', 'Smut', 'mature', 'ecchi', '18+'].includes(name))) {
      return 'Mature';
    }
    if (genreNames.some(name => ['romance', 'drama', 'psychological'].includes(name))) {
      return 'Teen';
    }
    return 'General';
  }

  /**
   * Generate keywords for comic with enhanced long-tail keywords
   */
  public generateComicKeywords(comic: Comic): string {
    const longTailKeywords = this.generateLongTailKeywords(comic.title, 'comic');
    
    const keywords = [
      ...longTailKeywords.slice(0, 8), // Add top 8 long-tail keywords
    ];
    // Add status-based keywords
    if (comic.status === 0) {
      keywords.push(`${comic.title} đang cập nhật`, 'truyện mới nhất');
    } else {
      keywords.push(`${comic.title} hoàn thành`, 'truyện full');
    }

    return keywords.filter(Boolean).join(', ');
  }

  /**
   * Generate article structured data for chapters optimized for comic reading
   */
  generateChapterSchema(comic: Comic, chapter: Chapter): any {
    const chapterName = `Chương ${chapter.slug}`;
    const chapterUrl = this.urlService.getFullChapterUrl(comic, chapter);

    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      'headline': `${comic.title} ${chapterName}`,
      'alternativeHeadline': `Đọc ${comic.title} ${chapterName} Online Miễn Phí`,
      'description': `Đọc ${chapterName} của truyện ${comic.title} online miễn phí tại ${this.siteName}. Chất lượng cao, tải nhanh.`,
      'image': comic.coverImage || this.urlService.getUrl(this.defaultImage),
      'url': chapterUrl,
      'datePublished': chapter.updateAt,
      'dateModified': chapter.updateAt,
      'author': this.generateAuthorObject(comic.author),
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.urlService.baseUrl,
        'logo': {
          '@type': 'ImageObject',
          'url': `${this.urlService.baseUrl}/logo.png`,
          'width': 136,
          'height': 64
        }
      },
      'mainEntityOfPage': {
        '@type': 'WebPage',
        '@id': chapterUrl
      },
      'isPartOf': {
        '@type': 'Book',
        'name': comic.title,
        'url': `${this.urlService.getFullComicUrl(comic)}`,
        'author': this.generateAuthorObject(comic.author)
      },
      'genre': comic.genres?.map((g) => g.title) || [],
      'inLanguage': 'vi',
      'keywords': `${comic.title}, ${chapterName}, truyện tranh online, đọc truyện miễn phí, manga, manhwa`,
      'articleSection': 'Truyện Tranh',
      'pagination': chapter.slug || chapter.id,
      'position': chapter.slug || chapter.id,
      'audience': {
        '@type': 'Audience',
        'audienceType': 'Comic Readers'
      }
    };
  }

  /**
   * Generate ItemList schema for comic listings (homepage, genre pages, etc.)
   */
  generateComicListSchema(comics: Comic[], listName: string, listDescription?: string): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      'name': listName,
      'description': listDescription || `Danh sách ${listName.toLowerCase()} tại ${this.siteName}`,
      'numberOfItems': comics.length,
      'itemListOrder': 'https://schema.org/ItemListOrderDescending',
      'itemListElement': comics.map((comic, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'item': {
          '@type': 'Book',
          'name': comic.title,
          'url': `${this.urlService.getFullComicUrl(comic)}`,
          'image': comic.coverImage,
          'author': this.generateAuthorObject(comic.author),
          'dateModified': comic.updateAt,
        }
      }))
    };
  }

  /**
   * Generate FAQ schema for comic pages
   */
  generateComicFAQSchema(comic: Comic): any {
    const latestChapter = comic.chapters?.[0]?.slug;
    const status = comic.status === 0 ? 'Đang cập nhật' : 'Hoàn thành';
    const totalChapters = comic.numChapter;

    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      'mainEntity': [
        {
          '@type': 'Question',
          'name': `${comic.title} có bao nhiêu chương?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `${comic.title} hiện tại có ${totalChapters} chương, chương mới nhất là chương ${latestChapter}.`
          }
        },
        {
          '@type': 'Question',
          'name': `Tình trạng của ${comic.title} như thế nào?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `${comic.title} hiện tại đang ở trạng thái ${status}.`
          }
        },
        {
          '@type': 'Question',
          'name': `Đọc ${comic.title} ở đâu miễn phí?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `Bạn có thể đọc ${comic.title} miễn phí tại ${this.siteName} với chất lượng hình ảnh cao và cập nhật nhanh nhất.`
          }
        },
        {
          '@type': 'Question',
          'name': `${comic.title} thuộc thể loại gì?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `${comic.title} thuộc thể loại ${comic.genres?.map((g) => g.title).join(', ') || 'Đang cập nhật'}.`
          }
        }
      ]
    };
  }

  /**
   * Generate Organization schema for the website
   */
  generateOrganizationSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      'name': this.siteName,
      'url': this.urlService.baseUrl,
      'logo': {
        '@type': 'ImageObject',
        'url': `${this.urlService.baseUrl}/logo.png`,
        'width': 136,
        'height': 64
      },
      'description': 'Website đọc truyện tranh online hàng đầu Việt Nam với kho tàng manga, manhwa, manhua phong phú',
      'foundingDate': '2024',
      'contactPoint': {
        '@type': 'ContactPoint',
        'contactType': 'customer service',
        'availableLanguage': 'Vietnamese'
      },
      'sameAs': [
        'https://www.facebook.com/metruyenmoicom',
      ],
      'areaServed': 'VN',
      'serviceType': 'Online Comic Reading Platform'
    };
  }
  /**
 * Add critical resource preloads for performance
 */
  addCriticalPreloads(resources: Array<{ url: string, as: string, type?: string, crossorigin?: boolean }>): void {
    if (isPlatformBrowser(this.platformId)) {
      resources.forEach(resource => {
        const link = this.document.createElement('link');
        link.rel = 'preload';
        link.href = resource.url;
        link.as = resource.as;
        if (resource.type) link.type = resource.type;
        if (resource.crossorigin) link.crossOrigin = 'anonymous';
        link.setAttribute('fetchpriority', 'high');
        this.document.head.appendChild(link);
      });
    }
  }

  /**
   * Add prefetch for next navigation
   */
  addPrefetchLinks(urls: string[]): void {
    if (isPlatformBrowser(this.platformId)) {
      urls.forEach(url => {
        const link = this.document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        this.document.head.appendChild(link);
      });
    }
  }
}
