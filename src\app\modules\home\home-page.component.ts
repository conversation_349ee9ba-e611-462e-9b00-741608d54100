import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, On<PERSON><PERSON>roy, OnInit, PLATFORM_ID, ViewEncapsulation, inject } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';

import { AnouncementComponent } from '@components/common/anouncement/anouncement.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { RecentReadComponent } from '@components/common/recent-read/recent-read.component';
import { TopListComponent } from '@components/common/top-list/top-list.component';
import { TopUsersComponent } from '@components/common/top-users/top-users.component';
import { LazyLoadDirective } from '@directives/lazyload.directive';
import { SimpleCarouselComponent } from 'src/app/shared/simple-carousel/simple-carousel.component';
import { ComicDescriptionPipe } from "../../pines/description.pipe";
import { NumeralPipe } from "../../pines/numeral.pipe";
@Component({
  selector: 'main[app-home]',
  templateUrl: './home-page.component.html',
  styleUrl: './home-page.component.scss',
  standalone: true,
  imports: [CommonModule,
    TopListComponent,
    PaginationComponent,
    AnouncementComponent,
    // CarouselLandingComponent,
    LazyLoadDirective,
    GridComicComponent,
    RecentReadComponent,
    TopUsersComponent,
    SimpleCarouselComponent,
    RouterLink, ComicDescriptionPipe, NumeralPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    class: 'md:container w-full mx-auto'
  }
})
export class HomePageComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  public urlService = inject(UrlService);

  listComics: Comic[] = [];
  totalpage!: number;
  currentPage = 1;
  carouselComics: Comic[] = [];
  // SEO Content expansion state
  isContentExpanded = false;

  // TrackBy function for performance optimization
  trackByComicId = (index: number, comic: Comic): number => {
    return comic?.id ?? index;
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private comicService: ComicService,
    private seoService: SeoService,
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
  ) {
    super(cdr, platformId);
    this.initializeSEO();
  }

  private initializeSEO(): void {
    const seoData = {
      title: 'Đọc Truyện Tranh Online Miễn Phí - Top Website Manga, Manhwa, Manhua Việt Nam',
      description: 'Website đọc truyện tranh online hàng đầu Việt Nam với hơn 10,000+ bộ manga, manhwa, manhua chất lượng HD. Cập nhật nhanh nhất, không quảng cáo, hoàn toàn miễn phí. Đọc truyện tranh hay nhất 2025!',
      type: 'website' as const,
      url: this.urlService.getUrl('/'),
      canonical: this.urlService.getCanonicalUrl('/'),
      image: `${this.urlService.getUrl('/logo.png')}`,
      siteName: 'MeTruyenMoi',
      locale: 'vi_VN',
      twitterCard: 'summary_large_image' as const
    };

    this.seoService.setSEOData(seoData);

  }

  ngOnInit(): void {
    this.addSubscription(
      this.route.queryParams.pipe(this.takeUntilDestroy()).subscribe((params: any) => {
        const page = Number(params['page']) || 1;
        this.currentPage = page;
        this.refreshPage(page);
      })
    );

    this.comicService.getRecommendComics()
    .pipe(this.takeUntilDestroy())
    .subscribe((res: any) => {
      if (!res.data) return;

      this.carouselComics = res.data.slice(0, 10); // Limit to 10 items for performance

      this.safeMarkForCheck();
    })
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }


  refreshPage(page: number, size = 20): void {
    this.listComics = [];
    this.addSubscription(
      this.comicService.getComics({
        step: size.toString(),
        genre: '-1',
        page: page.toString(),
        sort: '1',
        status: '-1',
      })
        .pipe(this.takeUntilDestroy())
        .subscribe((res: any) => {
          this.totalpage = res.data!.totalpage;
          this.listComics = res.data!.comics;

          // Update SEO for pagination
          if (page > 1) {
            this.updatePaginationSEO(page);
          }

          // Add ItemList structured data for comics
          this.addComicsStructuredData();

          this.safeMarkForCheck();
        })
    );
  }

  private updatePaginationSEO(page: number): void {
    const title = `Trang ${page} - Truyện Tranh Mới Nhất`;
    const description = `Xem trang ${page} của danh sách truyện tranh mới nhất được cập nhật liên tục. Hơn 10,000+ bộ manga, manhwa, manhua chất lượng cao.`;

    this.seoService.setSEOData({
      title,
      description,
      url: this.urlService.getUrl('/'),
      type: 'website',
      canonical: this.urlService.getCanonicalUrl('/')
    });
  }

  private addComicsStructuredData(): void {
    if (this.listComics.length > 0) {
      // Use the enhanced comic list schema from SEO service


      // Get existing schemas
      const websiteSchema = this.seoService.generateWebsiteSchema();
      const organizationSchema = this.seoService.generateOrganizationSchema();
      const breadcrumbSchema = this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: this.urlService.getUrl('/') }
      ]);

      // Combine all schemas
      const combinedSchemas = [websiteSchema, organizationSchema, breadcrumbSchema];
      if (this.currentPage == 1) {
        const itemListSchema = this.seoService.generateComicListSchema(
          this.listComics.slice(0, 20), // Show top 20 for better performance
          'Truyện Tranh Mới Nhất',
          'Danh sách truyện tranh mới cập nhật hàng ngày tại MeTruyenMoi'
        );
        combinedSchemas.push(itemListSchema);
      }
      this.seoService.addStructuredData(combinedSchemas);
    }
  }

  // Simple carousel change handler (placeholder for future analytics / lazy load)
  onCarouselChanged(index: number): void {
    // reserved for tracking
    console.log('Carousel changed to index:', index);
    
  }


  // Toggle SEO content expansion
  toggleContent(): void {
    this.isContentExpanded = !this.isContentExpanded;
    this.safeMarkForCheck();
  }


}
