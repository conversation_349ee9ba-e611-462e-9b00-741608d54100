import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnChanges, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic } from '@schema';
import { ComicService } from '@services/comic.service';
import { HistoryService } from '@services/history.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';

@Component({
    selector: 'main[app-history-tag]',
    templateUrl: './history-page.component.html',
    styleUrl: './history-page.component.scss',
    standalone: false,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HistoryPageComponent extends OptimizedBaseComponent implements OnInit, OnChanges {
  comics: Comic[] = [];
  page = 1;
  totalpage = 1;
  comicPerPage = 14;
  isLoading = false;
  selectedComics: number[] = [];

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private hisService: HistoryService,
    private toastService: ToastService,
    private popupService: PopupService,
    private seoService: SeoService,
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object,
  ) {
    super(cd, platformId);

  }

  ngOnInit(): void {
    this.isLoading = true;
    this.seoService.setHistorySEO();
    this.route.queryParams.pipe(this.takeUntilDestroy()).subscribe((params: any) => {
      const page = Number(params['page']) || 1;
      this.page = page;

      this.refreshPage();
    });
  }

  ngOnChanges() {

  }

  refreshPage() {
    let his = this.hisService.GetHistorys();
    if (his.length === 0) {
      this.comics = [];
      this.isLoading = false;
      return;
    }
    this.totalpage = Math.floor((his.length - 1) / this.comicPerPage) + 1;

    his = his.slice(
      (this.page - 1) * this.comicPerPage,
      this.page * this.comicPerPage,
    );
    const ids = his.map((element) => element.id);

    this.comicService.getComicsByIds(ids).pipe(this.takeUntilDestroy()).subscribe((res: any) => {
      this.comics = res.data;
      console.log(this.comics);
      this.isLoading = false;
      this.safeMarkForCheck();
    });
  }


  onRemoveSelectedComics(ids: number[]) {
    if (!ids.length) {
      return;
    }
    this.selectedComics = ids;
    this.popupService
      .showConfirmPopup({
        title: 'Xóa lịch sử',
        message: this.message,
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
      })
      .then((result: any) => {
        const { isconfirm, isCancel } = result;
        if (isconfirm) {
          this.confirmDelete();
        }
        if (isCancel) {
          this.cancelDelete();
        }
      });
  }

  confirmDelete() {
    this.selectedComics.forEach((id) => this.hisService.RemoveHistory(id));
    this.toastService.show(ToastType.Success, 'Xóa truyện đã xem thành công');

  }

  cancelDelete() {

  }

  get message(): string {
    if (this.selectedComics.length > 1) {
      return `Bạn có chắc chắn muốn xóa <b>${this.selectedComics.length}</b> truyện khỏi danh sách truyện đã xem?`;
    }

    const [comicId] = this.selectedComics;
    const comic = this.comics.find((comic) => comic.id === comicId);
    return comic
      ? `Bạn có chắc chắn muốn xóa <b>${comic?.title}</b> khỏi danh sách truyện đã xem?`
      : '';
  }



}
