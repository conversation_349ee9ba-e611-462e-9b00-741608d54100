<ng-container *ngIf="comicData(); else empty">
  <ng-template
    [ngTemplateOutlet]="topRightTemplate || hotTagTemplate"
    [ngTemplateOutletContext]="{ comic: comicData() }"
  ></ng-template>
  <span class="top-left">
    <span class="star-icon">

      ★ 
    </span>
    {{ comic?.rating }}</span
  >
  <a
    class="container-card-v1"
    [routerLink]="comicRouterLink()"
    routerLinkActive="active"
    [title]="comicData()?.title"
  >
    <img
      appLazyLoad
      [dataSrc]="comicData()?.coverImage"
      [alt]="comicData()?.title"
      class="comic-card-image"
      onerror="this.src='/option2.png'"
    />

    <div
      class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"
    ></div>
    <div
      (mouseenter)="onHoverComic(true)"
      (mouseleave)="onHoverComic(false)"
      class="footer-card-v1 comic-info"
    >
      <h3 class="card-comic-title">{{ comicData()?.title }}</h3>
      <div class="comic-status-view">
        <div class="status-container">
          <div class="status-indicator ongoing"></div>
          <span class="status-text">{{ statusName() }}</span>
        </div>
        <div class="card-view-count">
          <img src="/icons/view.svg" alt="views" class="w-3 h-3" />
          <span class="view-count-text">
            {{ comicData()?.viewCount | numeral }}
          </span>
        </div>
      </div>
    </div>
  </a>

  <a *ngIf="hasChapters()" [routerLink]="chapterRouterLink()" class="card-footer">
    <p class="chapter-title">Chapter {{ (comic?.chapters)![0].slug }}</p>
    <span class="chapter-update">
      {{ comic?.updateAt | dateAgo }}
    </span>
  </a>
</ng-container>

<ng-template #empty>
    <div class="placeholder-image">
      <div class="placeholder-content">
        <svg
          class="view-count-icon"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          viewBox="0 0 20 18"
        >
          <path
            d="M18 0H2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm-5.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm4.376 10.481A1 1 0 0 1 16 15H4a1 1 0 0 1-.895-1.447l3.5-7A1 1 0 0 1 7.468 6a.965.965 0 0 1 .9.5l2.775 4.757 1.546-1.887a1 1 0 0 1 1.618.1l2.541 4a1 1 0 0 1 .028 1.011Z"
          />
        </svg>
      </div>
    </div>
</ng-template>

<ng-template #hotTagTemplate let-comic="comic">
  <div *ngIf="comic?.type" class="top-right">
    <p class="hot-tag-text">HOT</p>
    <p class="hot-tag-animated">HOT</p>
  </div>
</ng-template>

<ng-content></ng-content>
