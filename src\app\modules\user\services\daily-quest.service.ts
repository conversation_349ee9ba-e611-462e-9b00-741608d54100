import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, timer } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

import { UrlService } from '@services/url.service';
import {
  ClaimRewardResponse,
  DailyQuest,
  QuestHistory,
  UserQuestStats,
  WeeklyQuest
} from '../interfaces/daily-quest.interface';

@Injectable({
  providedIn: 'root'
})
export class QuestService {
  private readonly API_BASE = '/quest';

  // State management
  private dailyQuestsSubject = new BehaviorSubject<DailyQuest[]>([]);
  private weeklyQuestsSubject = new BehaviorSubject<WeeklyQuest[]>([]);
  private userStatsSubject = new BehaviorSubject<UserQuestStats | null>(null);

  // Public observables
  public dailyQuests$ = this.dailyQuestsSubject.asObservable();
  public weeklyQuests$ = this.weeklyQuestsSubject.asObservable();
  public userStats$ = this.userStatsSubject.asObservable();

  // Auto-refresh timer
  private refreshTimer$ = timer(0, 60000); // Check every minute

  constructor(
    private http: HttpClient,
    private urlService: UrlService
  ) {
    this.initializeData();
  }

  // Initialize data from API
  private initializeData(): void {
    this.loadDailyQuests();
    this.loadWeeklyQuests();
    this.loadUserStats();
    // this.checkAndRefreshQuests();
  }


  // API Methods
  getDailyQuests(): Observable<DailyQuest[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/daily`);
    return this.http.get<DailyQuest[]>(url).pipe(
      tap(quests => this.dailyQuestsSubject.next(quests)),
      catchError(error => {
        console.error('Error fetching daily quests:', error);
        return of([]);
      })
    );
  }

  getWeeklyQuests(): Observable<WeeklyQuest[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/weekly`);
    return this.http.get<WeeklyQuest[]>(url).pipe(
      tap(quests => this.weeklyQuestsSubject.next(quests)),
      catchError(error => {
        console.error('Error fetching weekly quests:', error);
        return of([]);
      })
    );
  }

  getUserStats(): Observable<UserQuestStats | null> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/user-stats`);
    return this.http.get<UserQuestStats>(url).pipe(
      tap(stats => this.userStatsSubject.next(stats)),
      catchError(error => {
        console.error('Error fetching user stats:', error);
        return of(null);
      })
    );
  }


  claimReward(questId: string): Observable<ClaimRewardResponse> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/${questId}/claim`);
    return this.http.post<ClaimRewardResponse>(url, {}).pipe(
      tap((response) => {
        if (response.success) {
          // Refresh quests and stats after successful claim
          this.loadDailyQuests();
          this.loadWeeklyQuests();
          this.loadUserStats();
        }
      }),
      catchError(error => {
        console.error('Error claiming reward:', error);
        return of({
          success: false,
          message: 'Có lỗi xảy ra khi nhận thưởng',
          rewards: []
        });
      })
    );
  }


  getQuestHistory(page: number = 1, pageSize: number = 20): Observable<QuestHistory[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/history`);
    const params = { page: page.toString(), pageSize: pageSize.toString() };
    return this.http.get<QuestHistory[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Error fetching quest history:', error);
        return of([]);
      })
    );
  }



  // Private helper methods
  private loadDailyQuests(): void {
    this.getDailyQuests().subscribe();
  }

  private loadWeeklyQuests(): void {
    this.getWeeklyQuests().subscribe();
  }

  private loadUserStats(): void {
    this.getUserStats().subscribe();
  }



  // Utility methods
  refreshAll(): void {
    this.initializeData();
  }

  getCurrentDailyQuests(): DailyQuest[] {
    return this.dailyQuestsSubject.value;
  }

  getCurrentWeeklyQuests(): WeeklyQuest[] {
    return this.weeklyQuestsSubject.value;
  }

  getCurrentUserStats(): UserQuestStats | null {
    return this.userStatsSubject.value;
  }


  // Helper methods for quest progress calculation
  getQuestProgress(questId: string): number {
    const dailyQuest = this.getCurrentDailyQuests().find(q => q.id === questId);
    if (dailyQuest) {
      return Math.min((dailyQuest.current / dailyQuest.target) * 100, 100);
    }

    const weeklyQuest = this.getCurrentWeeklyQuests().find(q => q.id === questId);
    if (weeklyQuest) {
      return Math.min((weeklyQuest.current / weeklyQuest.target) * 100, 100);
    }

    return 0;
  }

  isQuestCompleted(questId: string): boolean {
    const dailyQuest = this.getCurrentDailyQuests().find(q => q.id === questId);
    if (dailyQuest) {
      return dailyQuest.status === 'completed';
    }

    const weeklyQuest = this.getCurrentWeeklyQuests().find(q => q.id === questId);
    if (weeklyQuest) {
      return weeklyQuest.status === 'completed';
    }

    return false;
  }

  canClaimReward(questId: string): boolean {
    return this.isQuestCompleted(questId);
  }
}
