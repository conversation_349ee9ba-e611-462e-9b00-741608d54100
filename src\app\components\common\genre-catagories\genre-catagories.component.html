<!-- Genre Categories Container -->
<div class="genre-container scrollbar-style-1">
  <!-- Header -->
  <div class="genre-header">
    <div class="genre-title">
      <svg class="genre-title-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <circle cx="12" cy="12" r="10" />
        <path d="M12 16v-4m0-4h.01" />
      </svg>
      <h3 class="genre-title-text">Danh sách theo tên thể loại</h3>
    </div>

    <!-- Search Form -->
    <form class="genre-search" (submit)="$event.preventDefault()">
      <div class="search-input-wrapper">
        <svg class="search-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path d="M11 19a8 8 0 1 0 0-16 8 8 0 0 0 0 16m10 2-4.35-4.35" />
        </svg>
        <input
          id="genreSearch"
          class="search-input"
          type="search"
          placeholder="Tìm thể loại..."
          [value]="searchTerm"
          (input)="filterGenres($event)"
          autocomplete="off"
          spellcheck="false"
        />
        @if (searchTerm) {
        <button type="button" class="clear-search-btn" (click)="clearSearch()" title="Xóa tìm kiếm">
          <svg class="clear-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
            <line x1="18" y1="6" x2="6" y2="18" />
            <line x1="6" y1="6" x2="18" y2="18" />
          </svg>
        </button>
        }
      </div>
    </form>
  </div>

  <!-- Genre Lists -->
  <div class="genre-content">
    <!-- Countries Section -->
    @if (filteredCatagoriesGenre().get('countries')?.length) {
    <section class="genre-section">
      <div class="section-header">
        <h4 class="section-title">Theo các nước</h4>
        <div class="section-divider"></div>
      </div>
      <div class="genre-grid">
        @for (genre of filteredCatagoriesGenre().get('countries'); track genre.id) {
        <button
          class="genre-chip"
          [class]="getGenreClass(genre.id)"
          [routerLink]="routerLinkGenres ? ['the-loai',genre.slug] : null"
          (click)="clickGenre(genre)"
          (mouseenter)="genreHovered = genre"
          [title]="genre.description || genre.title"
        >
          <span class="genre-name">{{ genre.title }}</span>
          @if (statusGenres[genre.id] === 1) {
          <svg class="genre-check-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
          </svg>
          } @if (statusGenres[genre.id] === 2) {
          <svg class="genre-check-icon" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 12 6.41z"
            />
          </svg>
          }
        </button>
        }
      </div>
    </section>
    }

    <!-- Common Genres Section -->
    @if (filteredCatagoriesGenre().get('genreCommon')?.length) {
    <section class="genre-section">
      <div class="section-header">
        <h4 class="section-title">Thể loại</h4>
        <div class="section-divider"></div>
      </div>
      <div class="genre-grid">
        @for (genre of filteredCatagoriesGenre().get('genreCommon'); track genre.id) {
        <button
          class="genre-chip"
          [class]="getGenreClass(genre.id)"
          [routerLink]="routerLinkGenres ? ['the-loai',genre.slug] : null"
          (click)="clickGenre(genre)"
          (mouseenter)="genreHovered = genre"
          [title]="genre.description || genre.title"
        >
          <span class="genre-name">{{ genre.title }}</span>
          @if (statusGenres[genre.id] === 1) {
          <svg class="genre-check-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
          </svg>
          } @if (statusGenres[genre.id] === 2) {
          <svg class="genre-check-icon" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 12 6.41z"
            />
          </svg>
          }
        </button>
        }
      </div>
    </section>
    }

    <!-- No Results -->
    @if (searchTerm && !hasFilteredResults()) {
    <div class="no-results">
      <svg class="no-results-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <circle cx="11" cy="11" r="8" />
        <path d="m21 21-4.35-4.35" />
      </svg>
      <p class="no-results-text">Không tìm thấy thể loại nào phù hợp</p>
      <button class="clear-search-link" (click)="clearSearch()">Xóa bộ lọc</button>
    </div>
    }
  </div>

  <!-- Genre Hover Info -->
  @if (genreHovered) {
  <div class="genre-hover-info">
    <div class="hover-divider"></div>
    <div class="hover-content">
      <div class="hover-icon">
        <svg class="info-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <circle cx="12" cy="12" r="9" />
          <line x1="12" y1="8" x2="12.01" y2="8" />
          <polyline points="11 12 12 12 12 16 13 16" />
        </svg>
      </div>
      <div class="hover-text">
        <h5 class="hover-title">{{ genreHovered.title }}</h5>
        <p class="hover-description">{{ genreHovered.description || 'Không có mô tả' }}</p>
      </div>
    </div>
  </div>
  }
</div>
