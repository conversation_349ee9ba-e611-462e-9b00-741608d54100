import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
    selector: '[app-eye-icon]',
    templateUrl: './eye-icon.component.html',
    styleUrl: './eye-icon.component.scss',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule],

})
export class EyeIconComponent {
  @Input() show = false;

  get iconClass() {
    return 'h-5 w-5 text-neutral-400 hover:text-neutral-500';
  }
}
