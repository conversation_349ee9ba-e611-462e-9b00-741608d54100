<!-- Contact Page Container -->
<div class="contact-container">
  <!-- Header Section -->
  <div class="contact-header">
    <div class="contact-header-content">
      <h1 class="contact-title">
        <svg class="contact-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
        </svg>
        Liên Hệ Với Chúng Tôi
      </h1>
      <p class="contact-subtitle">
        Chúng tôi luôn sẵn sàng lắng nghe và hỗ trợ bạn
      </p>
      <div class="contact-meta">
        <span class="response-time">⏰ Phản hồi trong 24 giờ</span>
        <span class="support-status">🟢 <PERSON><PERSON> hoạt động</span>
      </div>
    </div>
  </div>

  <!-- Quick Contact Methods -->
  <section class="quick-contact-section">
    <h2 class="section-title">Liên hệ nhanh</h2>
    <div class="quick-contact-grid">
      <div class="contact-method-card">
        <div class="method-icon">📧</div>
        <h3>Email</h3>
        <p>cskh.metruyenmoi&#64;gmail.com</p>
        <a href="mailto:<EMAIL>" class="contact-btn">Gửi Email</a>
      </div>
      <div class="contact-method-card">
        <div class="method-icon">💬</div>
        <h3>Chat Trực Tuyến</h3>
        <p>Hỗ trợ tức thì</p>
        <button class="contact-btn" (click)="openLiveChat()">Bắt Đầu Chat</button>
      </div>
      <div class="contact-method-card">
        <div class="method-icon">📱</div>
        <h3>Mạng Xã Hội</h3>
        <p>Theo dõi chúng tôi</p>
        <button class="contact-btn" (click)="openSocialMedia()">Kết Nối</button>
      </div>
      <div class="contact-method-card">
        <div class="method-icon">❓</div>
        <h3>FAQ</h3>
        <p>Câu hỏi thường gặp</p>
        <a href="/faq" class="contact-btn">Xem FAQ</a>
      </div>
    </div>
  </section>

  <!-- Contact Form Section -->
  <section class="contact-form-section">
    <div class="form-container">
      <h2 class="section-title">Gửi Tin Nhắn</h2>
      <p class="form-description">
        Điền thông tin bên dưới và chúng tôi sẽ phản hồi bạn trong thời gian sớm nhất
      </p>
      
      <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="contact-form">
        <!-- Personal Information -->
        <div class="form-row">
          <div class="form-group">
            <label for="name" class="form-label">Họ và tên *</label>
            <input
              type="text"
              id="name"
              formControlName="name"
              class="form-input"
              placeholder="Nhập họ và tên của bạn"
              [class.error]="isFieldInvalid('name')"
            />
            <div *ngIf="isFieldInvalid('name')" class="error-message">
              Vui lòng nhập họ và tên
            </div>
          </div>
          <div class="form-group">
            <label for="email" class="form-label">Email *</label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="form-input"
              placeholder="<EMAIL>"
              [class.error]="isFieldInvalid('email')"
            />
            <div *ngIf="isFieldInvalid('email')" class="error-message">
              <span *ngIf="contactForm.get('email')?.errors?.['required']">Vui lòng nhập email</span>
              <span *ngIf="contactForm.get('email')?.errors?.['email']">Email không hợp lệ</span>
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="phone" class="form-label">Số điện thoại</label>
            <input
              type="tel"
              id="phone"
              formControlName="phone"
              class="form-input"
              placeholder="0123 456 789"
            />
          </div>
          <div class="form-group">
            <label for="subject" class="form-label">Chủ đề *</label>
            <select
              id="subject"
              formControlName="subject"
              class="form-select"
              [class.error]="isFieldInvalid('subject')"
            >
              <option value="">Chọn chủ đề</option>
              <option value="technical">Hỗ trợ kỹ thuật</option>
              <option value="account">Vấn đề tài khoản</option>
              <option value="content">Báo cáo nội dung</option>
              <option value="copyright">Khiếu nại bản quyền</option>
              <option value="suggestion">Góp ý, đề xuất</option>
              <option value="partnership">Hợp tác kinh doanh</option>
              <option value="other">Khác</option>
            </select>
            <div *ngIf="isFieldInvalid('subject')" class="error-message">
              Vui lòng chọn chủ đề
            </div>
          </div>
        </div>

        <!-- Message -->
        <div class="form-group">
          <label for="message" class="form-label">Tin nhắn *</label>
          <textarea
            id="message"
            formControlName="message"
            class="form-textarea"
            rows="6"
            placeholder="Mô tả chi tiết vấn đề hoặc câu hỏi của bạn..."
            [class.error]="isFieldInvalid('message')"
          ></textarea>
          <div *ngIf="isFieldInvalid('message')" class="error-message">
            Vui lòng nhập tin nhắn (tối thiểu 10 ký tự)
          </div>
          <div class="character-count">
            {{ contactForm.get('message')?.value?.length || 0 }}/1000 ký tự
          </div>
        </div>

        <!-- File Upload -->
        <div class="form-group">
          <label class="form-label">Đính kèm file (tùy chọn)</label>
          <div class="file-upload-area" (click)="fileInput.click()" [class.dragover]="isDragOver">
            <input
              #fileInput
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx"
              (change)="onFileSelect($event)"
              class="file-input"
            />
            <div class="upload-content">
              <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
              </svg>
              <p>Kéo thả file vào đây hoặc click để chọn</p>
              <span class="file-types">JPG, PNG, GIF, PDF, DOC (tối đa 10MB)</span>
            </div>
          </div>
          <div *ngIf="selectedFiles.length > 0" class="selected-files">
            <div *ngFor="let file of selectedFiles; let i = index" class="file-item">
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">({{ formatFileSize(file.size) }})</span>
              <button type="button" (click)="removeFile(i)" class="remove-file">×</button>
            </div>
          </div>
        </div>

        <!-- Privacy Agreement -->
        <div class="form-group">
          <label class="checkbox-label">
            <input
              type="checkbox"
              formControlName="agreePrivacy"
              class="checkbox-input"
            />
            <span class="checkbox-custom"></span>
            <span class="checkbox-text">
              Tôi đồng ý với 
              <a href="/chinh-sach-bao-mat" target="_blank" class="privacy-link">Chính sách bảo mật</a>
              và cho phép xử lý thông tin cá nhân *
            </span>
          </label>
          <div *ngIf="isFieldInvalid('agreePrivacy')" class="error-message">
            Vui lòng đồng ý với chính sách bảo mật
          </div>
        </div>

        <!-- Submit Button -->
        <div class="form-actions">
          <button
            type="submit"
            class="submit-btn"
            [disabled]="contactForm.invalid || isSubmitting"
            [class.loading]="isSubmitting"
          >
            <span *ngIf="!isSubmitting">
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
              </svg>
              Gửi Tin Nhắn
            </span>
            <span *ngIf="isSubmitting" class="loading-content">
              <div class="spinner"></div>
              Đang gửi...
            </span>
          </button>
        </div>
      </form>
    </div>
  </section>

  <!-- Contact Information -->
  <section class="contact-info-section">
    <h2 class="section-title">Thông Tin Liên Hệ</h2>
    <div class="contact-info-grid">
      <div class="info-card">
        <div class="info-icon">🏢</div>
        <h3>Thông tin website</h3>
        <div class="info-details">
          <p><strong>Tên:</strong> {{ siteName }}</p>
          <p><strong>Website:</strong> {{ host }}</p>
          <p><strong>Địa chỉ:</strong> Việt Nam</p>
        </div>
      </div>
      
      <div class="info-card">
        <div class="info-icon">📧</div>
        <h3>Email hỗ trợ</h3>
        <div class="info-details">
          <p><strong>Hỗ trợ chung:</strong> <a href="mailto:cskh.metruyenmoi&#64;gmail.com">cskh.metruyenmoi&#64;gmail.com</a></p>
          <p><strong>Báo cáo vi phạm:</strong> <a href="mailto:cskh.metruyenmoi&#64;gmail.com">cskh.metruyenmoi&#64;gmail.com</a></p>
          <p><strong>Bản quyền:</strong> <a href="mailto:cskh.metruyenmoi&#64;gmail.com">cskh.metruyenmoi&#64;gmail.com</a></p>
        </div>
      </div>
      
      <div class="info-card">
        <div class="info-icon">⏰</div>
        <h3>Thời gian hỗ trợ</h3>
        <div class="info-details">
          <p><strong>Hỗ trợ kỹ thuật:</strong> 24-48 giờ</p>
          <p><strong>Báo cáo vi phạm:</strong> 1-3 ngày làm việc</p>
          <p><strong>Khiếu nại bản quyền:</strong> 24 giờ</p>
          <p><strong>Vấn đề khẩn cấp:</strong> Trong ngày</p>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="faq-section">
    <h2 class="section-title">Câu Hỏi Thường Gặp</h2>
    <div class="faq-grid">
      <div class="faq-item" *ngFor="let faq of faqs; let i = index">
        <button 
          class="faq-question" 
          (click)="toggleFaq(i)"
          [class.active]="faq.isOpen"
        >
          <span>{{ faq.question }}</span>
          <svg class="faq-icon" [class.rotated]="faq.isOpen" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>
        <div class="faq-answer" [class.open]="faq.isOpen">
          <p>{{ faq.answer }}</p>
        </div>
      </div>
    </div>
  </section>
</div>

<!-- Success Modal -->
<div *ngIf="showSuccessModal" class="modal-overlay" (click)="closeSuccessModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="success-icon">✅</div>
    <h3>Gửi thành công!</h3>
    <p>Cảm ơn bạn đã liên hệ với chúng tôi. Chúng tôi sẽ phản hồi trong thời gian sớm nhất.</p>
    <button class="modal-btn" (click)="closeSuccessModal()">Đóng</button>
  </div>
</div>
