<div app-breadcrumb
  class="z-10 my-2 md:container mx-auto w-full flex"
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Thể loại', url: '/the-loai' }
  ]"
></div>
<section class="md:container mx-auto px-2 py-8">

  <div class="flex items-center justify-between mb-5">
    <div>
      <h1 class="text-2xl font-bold">Danh mục thể loại</h1>
      <p class="text-sm opacity-80">Khám phá truyện theo sở thích. Dùng ô tìm kiếm để lọc nhanh.</p>
    </div>
    <div class="hidden md:block w-64">
      <input [(ngModel)]="q" placeholder="Tìm thể loại..." class="w-full px-3 py-2 rounded-lg bg-white/70 dark:bg-zinc-900/60 border border-zinc-200/60 dark:border-zinc-800 outline-none focus:ring focus:ring-blue-500/30" />
    </div>
  </div>

  <div class="md:hidden mb-4">
    <input [(ngModel)]="q" placeholder="Tìm thể loại..." class="w-full px-3 py-2 rounded-lg bg-white/70 dark:bg-zinc-900/60 border border-zinc-200/60 dark:border-zinc-800 outline-none focus:ring focus:ring-blue-500/30" />
  </div>

  <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
    <a class="group bg-white/80 dark:bg-zinc-900/60 rounded-lg p-4 border border-zinc-200/60 dark:border-zinc-800 hover:border-blue-500 transition" [routerLink]="['/the-loai', g.slug]" *ngFor="let g of (genres$ | async) | genreFilter:q">
      <div class="font-semibold group-hover:text-blue-600">{{ g.title }}</div>
      <div class="text-xs opacity-70 mt-1">{{ g.description || ('Thể loại truyện ' + g.title) }}</div>
    </a>
  </div>
</section>
