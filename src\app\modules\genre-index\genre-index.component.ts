import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { Genre } from '@schema';
import { GenreService } from '@services/genre.service';
import { SeoService } from '@services/seo.service';
import { Observable } from 'rxjs';
import { GenreFilterPipe } from './genre-filter.pipe';

@Component({
  selector: 'main[app-genre-index]',
  templateUrl: './genre-index.component.html',
  styleUrls: ['./genre-index.component.scss'],
  standalone: true,
  imports: [FormsModule,
    GenreFilterPipe,
    CommonModule,
    RouterModule,
    BreadcrumbComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GenreIndexComponent implements OnInit {
  genres$!: Observable<Genre[]>;
  q = '';
  constructor(private seo: SeoService, private genre: GenreService) { }
  ngOnInit(): void {
    this.seo.setGenreIndexSEO();
    this.genres$ = this.genre.getGenres();
  }
}
