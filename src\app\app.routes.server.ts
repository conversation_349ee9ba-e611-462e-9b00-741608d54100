import { RenderMode, ServerRoute } from '@angular/ssr';

export const serverRoutes: ServerRoute[] = [
  // {
  //   path: 'tro-ly-ai',
  //   renderMode: RenderMode.Prerender
  // },
  // {
  //   path: 'dieu-khoan',
  //   renderMode: RenderMode.Prerender
  // },
  // {
  //   path: 'dong-bo-truyen',
  //   renderMode: RenderMode.Prerender
  // },
  // {
  //   path: 'lien-he',
  //   renderMode: RenderMode.Prerender
  // },
  // {
  //   path: 'chinh-sach-bao-mat',
  //   renderMode: RenderMode.Prerender
  // },
  // {
  //   path: 'lich-su',
  //   renderMode: RenderMode.Prerender
  // },
  
  {
    path: '**',
    renderMode: RenderMode.Server
  }
];
