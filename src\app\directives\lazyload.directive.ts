import { isPlatformBrowser } from '@angular/common';
import { AfterViewInit, Directive, ElementRef, Inject, Input, OnDestroy, PLATFORM_ID, Renderer2 } from '@angular/core';

@Directive({
  selector: 'img[appLazyLoad]'
})
export class LazyLoadDirective implements AfterViewInit, OnDestroy {
  @Input() dataSrc?: string;
  placeholder = '/images/placeholder.png';
  @Input() lazyLoad = true;
  private observer?: IntersectionObserver;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    @Inject(PLATFORM_ID) private platformId: object
  ) { }

  ngOnInit(): void {
    if(!this.lazyLoad) {
      this.loadImage();
    }
  }

  ngAfterViewInit(): void {
    if (!this.lazyLoad) return;
    // Set the placeholder image immediately after the view is initialized.
    this.renderer.setAttribute(this.el.nativeElement, 'src', this.placeholder);

    if (!isPlatformBrowser(this.platformId)) return;

    if ('IntersectionObserver' in window ) {
      this.observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {          
          if (entry.isIntersecting) {
            this.loadImage();
            this.observer?.unobserve(this.el.nativeElement);
          }
        });
      }, {
        root: null,
        rootMargin: '0px 0px 50px 0px',
        threshold: 0.01
      });

      this.observer.observe(this.el.nativeElement);
    } else {
      // Fallback for browsers that don't support IntersectionObserver
      this.loadImage();
    }
  }

  private loadImage(): void {
    const finalSrc = this.dataSrc || '/option2.png';
    this.renderer.setAttribute(this.el.nativeElement, 'src', finalSrc);
  }

  ngOnDestroy(): void {
    this.observer?.disconnect();
  }
}