

.item-card {
  @apply relative bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700;
  @apply transition-all duration-200 ease-in-out cursor-pointer;
  @apply hover:shadow-md hover:border-neutral-300 dark:hover:border-neutral-600;
  @apply transform hover:scale-105;

  &--small {
    @apply p-2 min-h-[80px];
    
    .item-card__icon-container {
      @apply w-12 h-12;
    }
    
    .item-card__name {
      @apply text-sm font-medium;
    }
    
    .item-card__rarity-text {
      @apply text-xs;
    }
  }

  &--medium {
    @apply p-3 min-h-[120px];
    
    .item-card__icon-container {
      @apply w-16 h-16;
    }
    
    .item-card__name {
      @apply text-base font-semibold;
    }
  }

  &--large {
    @apply p-4 min-h-[160px];
    
    .item-card__icon-container {
      @apply w-20 h-20;
    }
    
    .item-card__name {
      @apply text-lg font-bold;
    }
  }

  // Rarity styles
  &--common {
    .item-card__rarity-border {
      @apply border-neutral-400;
    }
  }

  &--uncommon {
    .item-card__rarity-border {
      @apply border-lime-500;
    }
  }

  &--rare {
    .item-card__rarity-border {
      @apply border-sky-500;
    }
  }

  &--epic {
    .item-card__rarity-border {
      @apply border-purple-500;
    }
  }

  &--legendary {
    .item-card__rarity-border {
      @apply border-red-500;
    }
  }

  // State styles
  &--selected {
    @apply ring-2 ring-sky-500 border-sky-500;
  }

  &--hovered {
    @apply shadow-lg;
  }

  &--equipped {
    @apply bg-sky-50 dark:bg-sky-900/20 border-sky-300 dark:border-sky-600;
  }

  &--expired {
    @apply opacity-60 grayscale;
  }

  &--expiring {
    @apply border-yellow-400 dark:border-yellow-500;
  }
}

.item-card__selection {
  @apply absolute top-2 left-2 z-10;
}

.item-card__checkbox {
  @apply w-4 h-4 text-sky-600 bg-neutral-100 border-neutral-300 rounded;
  @apply focus:ring-sky-500 dark:focus:ring-sky-600 dark:ring-offset-neutral-800;
  @apply dark:bg-neutral-700 dark:border-neutral-600;
}

.item-card__icon {
  @apply flex justify-center mb-2;
}

.item-card__icon-container {
  @apply relative rounded-lg overflow-hidden;
}

.item-card__image {
  @apply w-full h-full object-cover;
}

.item-card__rarity-border {
  @apply absolute inset-0 border-2 rounded-lg pointer-events-none;
}

.item-card__equipped-badge {
  @apply absolute -top-1 -right-1 w-5 h-5 bg-sky-500 rounded-full;
  @apply flex items-center justify-center text-white text-xs;
}

.item-card__quantity {
  @apply absolute -bottom-1 -right-1 bg-neutral-900 text-white text-xs;
  @apply px-1.5 py-0.5 rounded-full min-w-[20px] text-center;
}

.item-card__expiry-warning {
  @apply absolute -top-1 -left-1 w-5 h-5 bg-yellow-500 rounded-full;
  @apply flex items-center justify-center text-white text-xs;
}

.item-card__expired-overlay {
  @apply absolute inset-0 bg-black bg-opacity-60 rounded-lg;
  @apply flex items-center justify-center text-white text-xs font-semibold;
}

.item-card__info {
  @apply flex-1 min-w-0;
}

.item-card__name {
  @apply text-neutral-900 dark:text-neutral-100 truncate mb-1;
}

.item-card__rarity {
  @apply mb-1;
}

.item-card__rarity-text {
  @apply text-neutral-600 dark:text-neutral-400 font-medium;
}

.item-card__category {
  @apply flex items-center gap-1 text-neutral-500 dark:text-neutral-400 text-sm mb-2;
}

.item-card__description {
  @apply text-neutral-600 dark:text-neutral-400 text-sm line-clamp-2 mb-2;
}

.item-card__expiry {
  @apply flex items-center gap-1 text-yellow-600 dark:text-yellow-400 text-xs;
}

.item-card__actions {
  @apply flex items-center gap-2 mt-2;
}

.item-card__quick-action {
  @apply flex items-center gap-1 px-2 py-1 rounded text-sm;
  @apply bg-neutral-100 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300;
  @apply hover:bg-neutral-200 dark:hover:bg-neutral-600 transition-colors;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;

  &--primary {
    @apply bg-sky-500 text-white hover:bg-sky-600;
  }
}

.item-card__actions-menu {
  @apply relative;
}

.item-card__actions-toggle {
  @apply w-8 h-8 rounded-full bg-neutral-100 dark:bg-neutral-700;
  @apply flex items-center justify-center text-neutral-600 dark:text-neutral-400;
  @apply hover:bg-neutral-200 dark:hover:bg-neutral-600 transition-colors;
}

.item-card__actions-dropdown {
  @apply absolute right-0 top-full mt-1 bg-white dark:bg-neutral-800;
  @apply border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg;
  @apply min-w-[120px] z-20;
}

.item-card__action-item {
  @apply w-full flex items-center gap-2 px-3 py-2 text-sm text-left;
  @apply text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  @apply first:rounded-t-lg last:rounded-b-lg;

  &--primary {
    @apply text-sky-600 dark:text-sky-400;
  }
}

.item-card__loading {
  @apply absolute inset-0 bg-white bg-opacity-80 dark:bg-neutral-800 dark:bg-opacity-80;
  @apply flex items-center justify-center rounded-lg;
}

.item-card__spinner {
  @apply w-6 h-6 border-2 border-neutral-300 border-t-sky-500 rounded-full animate-spin;
}

// Icon styles (assuming icon font or SVG icons)
[class^="icon-"], [class*=" icon-"] {
  @apply inline-block w-4 h-4;
}

// Responsive adjustments
@screen sm {
  .item-card--small {
    @apply min-h-[90px];
  }
  
  .item-card--medium {
    @apply min-h-[130px];
  }
  
  .item-card--large {
    @apply min-h-[170px];
  }
}

// Dark mode specific adjustments
@media (prefers-color-scheme: dark) {
  .item-card {
    &--equipped {
      @apply bg-sky-900/30;
    }
  }
}
