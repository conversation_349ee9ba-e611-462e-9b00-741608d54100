// Modern Recent Read Component - Comic Website Design
.rr-recent-read-container {
  @apply bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 shadow-sm overflow-hidden;
}

// Enhanced Header
.rr-recent-read-header {
  @apply flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700;
}

.rr-header-title-section {
  @apply flex items-center gap-2;
}

.rr-header-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.rr-header-title {
  @apply text-base sm:text-lg font-bold text-gray-700 dark:text-light-text uppercase;
}

.rr-header-count {
  @apply px-2 py-1 bg-primary-100 text-white text-xs font-bold rounded-full;
}

.rr-header-actions {
  @apply flex items-center;
}

.rr-view-more-button {
  @apply flex items-center gap-2 px-2 py-1 text-neutral-600 dark:text-neutral-400 hover:text-primary-100 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-lg;
}

.rr-view-more-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Content Area
.rr-recent-read-content {
  @apply bg-white dark:bg-dark-bg h-[164px] flex justify-center;
}

// Empty State
.rr-empty-state {
  @apply text-center flex  items-center justify-center h-full px-4;
}

.rr-empty-state-content {
  @apply space-y-2;
}

.rr-empty-state-icon {
  @apply w-16 h-16 mx-auto text-neutral-300 dark:text-neutral-600;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.rr-empty-state-title {
  @apply text-lg font-semibold text-neutral-900 dark:text-light-text;
}

.rr-empty-state-description {
  @apply text-sm text-neutral-500 dark:text-neutral-400;
}

// Comic List
.rr-comic-list {
  @apply space-y-1.5 p-1.5 w-full;
}

.rr-comic-item {
  @apply border-b border-neutral-200 dark:border-neutral-700 last:border-b-0;
}

.rr-comic-item-content {
  @apply flex items-center gap-2 p-1 ;
}

// Comic Image
.rr-comic-image-container {
  @apply relative flex-shrink-0;
}

.rr-comic-image-link {
  @apply relative block overflow-hidden rounded-lg;
}

.rr-comic-image {
  @apply w-12 h-16 object-cover;
}

.rr-image-overlay {
  @apply absolute inset-0 bg-black/0 hover:bg-black/10;
}

// Comic Info
.rr-comic-info {
  @apply flex-1 min-w-0 space-y-2;
}

.rr-comic-main-info {
  @apply space-y-1;
}

.rr-comic-title {
  @apply text-sm font-bold text-neutral-900 dark:text-light-text line-clamp-1;
}

.rr-comic-title-link {
  @apply hover:text-primary-100;
}

.rr-comic-chapter {
  @apply flex items-center gap-2 text-xs text-neutral-600 dark:text-neutral-400;
}

.rr-chapter-icon {
  @apply w-4 h-4 flex-shrink-0;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.rr-comic-chapter-link {
  @apply hover:text-primary-100 hover:underline line-clamp-1;
}

// Comic Meta
.rr-comic-meta {
  @apply flex flex-row items-start gap-1 justify-between;
}

.rr-comic-rating {
  @apply flex items-center gap-1 text-xs text-orange-500 dark:text-orange-400;
}

.rr-rating-icon {
  @apply size-4;
  fill: currentColor;
  stroke: none;
}

.rr-rating-value {
  @apply font-medium;
}

.rr-comic-update-time {
  @apply flex items-center gap-1 text-xs text-neutral-500 dark:text-neutral-400;
}

.rr-time-icon {
  @apply w-3 h-3;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.rr-time-value {
  @apply font-medium;
}


