
  <!-- Enhanced Header -->
  <div class="rr-recent-read-header">
    <div class="rr-header-title-section">
      <svg class="rr-header-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path
          d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"
        />
      </svg>
      <h2 class="rr-header-title"><PERSON><PERSON><PERSON> g<PERSON>n đ<PERSON></h2>
      <span class="rr-header-count" *ngIf="this.nComics > 0">{{ this.nComics }}</span>
    </div>

    <div class="rr-header-actions">
      <a [routerLink]="['/lich-su']" class="rr-view-more-button" aria-label="Xem thêm">
        <svg class="rr-view-more-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <polyline points="9,18 15,12 9,6" />
        </svg>
      </a>
    </div>
  </div>

  <!-- Enhanced Comic List -->
  <div class="rr-recent-read-content relative">
    <!-- Empty State -->

    <div app-spinner *ngIf="isLoading; else comicContent" [sizeSpinner]="'40'"></div>
    <ng-template #comicContent>
      <div *ngIf="listComics.length === 0; else comicList" class="rr-empty-state">
        <div class="rr-empty-state-content">
          <svg class="rr-empty-state-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
          </svg>
          <p class="rr-empty-state-description">Bạn chưa đọc truyện nào gần đây</p>
        </div>
      </div>

      <ng-template #comicList>
        <div class="rr-comic-list">
          <div *ngFor="let comic of listComics; trackBy: trackByComicId" class="rr-comic-item rr-comic-item-content">
              <!-- Comic Image -->
              <div class="rr-comic-image-container">
                <a
                  [routerLink]="this.urlService.getComicDetailUrl(comic)"
                  class="rr-comic-image-link"
                >
                  <img
                    class="rr-comic-image"
                    [dataSrc]="comic.coverImage"
                    appLazyLoad
                    [alt]="comic.title"
                    onerror="this.src='/option2.png'"
                  />
                  <div class="rr-image-overlay"></div>
                </a>
              </div>

              <!-- Comic Info -->
              <div class="rr-comic-info">
                <div class="rr-comic-main-info">
                  <!-- Title -->
                  <h3 class="rr-comic-title">
                    <a
                      [routerLink]="this.urlService.getComicDetailUrl(comic)"
                      class="rr-comic-title-link"
                      >{{ comic.title }}</a
                    >
                  </h3>

                  <!-- Chapter -->
                  <div class="rr-comic-chapter" *ngIf="comic.chapters && comic.chapters.length > 0">
                    <svg class="rr-chapter-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                      <polyline points="14,2 14,8 20,8" />
                      <line x1="16" y1="13" x2="8" y2="13" />
                      <line x1="16" y1="17" x2="8" y2="17" />
                      <polyline points="10,9 9,9 8,9" />
                    </svg>
                    <a
                      [routerLink]="this.urlService.getChapterDetailUrl(comic, comic.chapters[0])"
                      class="rr-comic-chapter-link"
                      >{{ comic.chapters[0].title }}</a
                    >
                  </div>
                </div>

                <!-- Comic Meta -->
                <div class="rr-comic-meta">
                  <!-- Rating -->
                  <div class="rr-comic-rating">
                    <svg class="rr-rating-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <polygon
                        points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"
                      />
                    </svg>
                    <span class="rr-rating-value">{{ comic.rating }}</span>
                  </div>

                  <!-- Update Time -->
                  <div class="rr-comic-update-time">
                    <svg class="rr-time-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <circle cx="12" cy="12" r="10" />
                      <polyline points="12,6 12,12 16,14" />
                    </svg>
                    <span class="rr-time-value">{{ comic.updateAt | dateAgo }}</span>
                  </div>
                </div>
              </div>


          </div>
        </div>
      </ng-template>
    </ng-template>


    <!-- Comic Items -->
  </div>
