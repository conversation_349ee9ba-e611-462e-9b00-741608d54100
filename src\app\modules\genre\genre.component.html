<div
  app-breadcrumb
  class="z-10 mt-2 mb-5 md:container mx-auto w-full flex"
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Thể loại', url: '/the-loai' },
    { label: currentGenre()?.title || '<PERSON>ang tải...', url: '/the-loai/' + (currentGenre()?.slug || '') }
  ]"
></div>

<div class="md:container mx-auto w-full dark:text-light-text px-2 md:px-0">
  <!-- Genre Header Section -->

  <div class="grid grid-cols-1 xl:grid-cols-12 gap-4">
    <!-- Comics Grid Section (9/12 columns on xl) -->
    <div class="xl:col-span-9 col-span-1">
      <div class="genre-header">
        <div class="genre-info">
          <svg
            class="size-5 mr-2 mb-1 inline-block"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
              <path
                opacity="0.34"
                d="M5 10H7C9 10 10 9 10 7V5C10 3 9 2 7 2H5C3 2 2 3 2 5V7C2 9 3 10 5 10Z"
                stroke="#292D32"
                stroke-width="1.5"
                stroke-miterlimit="10"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
              <path
                d="M17 10H19C21 10 22 9 22 7V5C22 3 21 2 19 2H17C15 2 14 3 14 5V7C14 9 15 10 17 10Z"
                stroke="#292D32"
                stroke-width="1.5"
                stroke-miterlimit="10"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
              <path
                opacity="0.34"
                d="M17 22H19C21 22 22 21 22 19V17C22 15 21 14 19 14H17C15 14 14 15 14 17V19C14 21 15 22 17 22Z"
                stroke="#292D32"
                stroke-width="1.5"
                stroke-miterlimit="10"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
              <path
                d="M5 22H7C9 22 10 21 10 19V17C10 15 9 14 7 14H5C3 14 2 15 2 17V19C2 21 3 22 5 22Z"
                stroke="#292D32"
                stroke-width="1.5"
                stroke-miterlimit="10"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </g>
          </svg>
          <h1 class="genre-title inline-block">Thể loại {{ currentGenre()?.title }}</h1>
          <span class="ml-2 text-xl">({{ totalComicsCount() }} truyện)</span>

          <div class="genre-description" *ngIf="currentGenre()">
            <p class="description-text">{{ genreDescription() }}</p>
          </div>
        </div>

        <!-- Enhanced Filter Controls -->
        <div class="genre-filters">
          <div class="filter-controls">
            <div class="filter-group">
              <label class="filter-label">Sắp xếp theo</label>
              <div
                app-selection-2
                class="filter-select"
                [options]="dataView.sorts"
                (selectedValueChange)="onSortOptionChange($event)"
                [selectedValue]="selectOptions.sorts.value"
              ></div>
            </div>

            <div class="filter-group">
              <label class="filter-label">Trạng thái</label>
              <div
                app-selection-2
                class="filter-select"
                [options]="dataView.status"
                (selectedValueChange)="onStatusOptionChange($event)"
                [selectedValue]="selectOptions.status.value"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div app-grid-comic id="listComic" [listComics]="listComics()" [isLoading]="isLoading()"></div>
      <nav
        aria-label="Pagination navigation"
        app-pagination
        [fragment]="'listComic'"
        [queryParams]="queryParams"
        [totalpage]="totalpage()"
        [currentPage]="currentPage"
        class="mt-8"
      ></nav>
    </div>
    <!-- Genres List (3/12 columns on xl) -->
    <div class="list-genres xl:col-span-3 col-span-1">
      <div class="rounded-2xl shadow-sm">
        <h2
          class="text-xl uppercase font-bold mb-3 text-gray-900 dark:text-light-text flex items-center"
        >
          <svg
            class="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 24 24"
          >
            <path d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          Danh sách thể loại
        </h2>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-2 gap-2">
          <a
            *ngFor="let genre of genres(); trackBy: trackByGenreId"
            [title]="genre.title"
            [routerLink]="['/the-loai', genre.slug]"
            class="px-4 py-2 rounded-md bg-gray-100 dark:bg-neutral-700 text-gray-700 dark:text-gray-200 dark:hover:text-white text-sm font-medium no-underline"
          >
            {{ genre.title }}
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
