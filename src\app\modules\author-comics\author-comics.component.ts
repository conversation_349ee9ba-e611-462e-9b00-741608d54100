import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OptimizedBaseComponent } from '@components/base/optimized-base.component';
import { Comic } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';

@Component({
  selector: 'main[app-author-comics]',
  standalone: false,
  templateUrl: './author-comics.component.html',
  styleUrl: './author-comics.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AuthorComicsComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  listComics: Comic[] = [];
  author: string = '';
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // Breadcrumb data
  breadcrumbItems = [
    { label: 'Trang chủ', url: '/' },
    { label: 'Tác giả', url: '' }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private comicService: ComicService,
    private seoService: SeoService,
    private urlService: UrlService,
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit() {
    this.addSubscription(
      this.route.params.pipe(
        this.takeUntilDestroy()
      ).subscribe((params:any) => {
        const author = params['author'];
        if (author) {
          this.author = decodeURIComponent(author);
          this.updateBreadcrumb();
          this.setupSEO();
          this.loadComicsByAuthor();
        } else {
          this.router.navigate(['/404']);
        }
      })
    );
  }

  /**
   * Load comics by author
   */
  private loadComicsByAuthor() {
    if (!this.author) return;

    this.isLoading = true;
    this.hasError = false;
    this.listComics = [];
    this.safeMarkForCheck();

    this.addSubscription(
      this.comicService.getComicsByAuthor(this.author, 20).pipe(
        this.takeUntilDestroy()
      ).subscribe({
        next: (response:any) => {
          this.isLoading = false;
          if (response.status === 1 && response.data) {
            this.listComics = response.data;
          } else {
            this.hasError = true;
            this.errorMessage = response.message || 'Không thể tải danh sách truyện';
          }
          this.safeMarkForCheck();
        },
        error: (error) => {
          this.isLoading = false;
          this.hasError = true;
          this.errorMessage = 'Có lỗi xảy ra khi tải danh sách truyện';
          console.error('Error loading comics by author:', error);
          this.safeMarkForCheck();
        }
      })
    );
  }

  /**
   * Update breadcrumb with author name
   */
  private updateBreadcrumb() {
    this.breadcrumbItems = [
      { label: 'Trang chủ', url: '/' },
      { label: 'Tác giả', url: '' },
      { label: this.author, url: `/tac-gia/${encodeURIComponent(this.author)}` }
    ];
  }

  /**
   * Setup SEO for author page
   */
  private setupSEO() {
    const seoConfig = {
      title: `Truyện của tác giả ${this.author} - MeTruyenMoi`,
      description: `Danh sách tất cả các bộ truyện tranh của tác giả ${this.author}. Khám phá những tác phẩm hay nhất từ ${this.author} tại MeTruyenMoi.`,
      keywords: `${this.author}, tác giả ${this.author}, truyện tranh ${this.author}, manga ${this.author}, comic author, mê truyện mới`,
      url: `${this.urlService.baseUrl}/tac-gia/${encodeURIComponent(this.author)}`,
      type: 'website' as const,
      siteName: 'MeTruyenMoi'
    };

    this.seoService.setSEOData(seoConfig);

    // Add breadcrumb schema
    const breadcrumbs = [
      { name: 'Trang chủ', url: '/' },
      { name: 'Tác giả', url: '/tac-gia' },
      { name: this.author, url: `/tac-gia/${encodeURIComponent(this.author)}` }
    ];

    this.seoService.generateBreadcrumbSchema(breadcrumbs);
  }

  /**
   * Navigate to comic detail
   */
  onComicClick(comic: Comic) {
    if (comic.url && comic.id) {
      this.router.navigate([`/truyen-tranh/${comic.url}-${comic.id}`]);
    }
  }

  /**
   * Retry loading comics
   */
  onRetry() {
    this.loadComicsByAuthor();
  }
}
