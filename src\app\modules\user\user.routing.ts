import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./components/user-profile-container/user-profile-container.component').then(m => m.UserProfileContainerComponent),
    children: [
      {
        path: '',
        redirectTo: 'profile',
        pathMatch: 'full'
      },
      {
        path: 'profile',
        loadComponent: () => import('./components/user-header/user-header.component').then(m => m.UserHeaderComponent)
      },
      {
        path: 'daily-quests',
        loadComponent: () => import('./components/daily-quests/daily-quests.component').then(m => m.DailyQuestsComponent)
      },
      {
        path: 'favorites',
        loadComponent: () => import('./components/favorite-comics/favorite-comics.component').then(m => m.FavoriteComicsComponent)
      },
      {
        path: 'history',
        loadComponent: () => import('./components/reading-history/reading-history.component').then(m => m.ReadingHistoryComponent)
      },
      {
        path: 'stats',
        loadComponent: () => import('./components/user-statistics/user-statistics.component').then(m => m.UserStatisticsComponent)
      },
      {
        path: 'achievements',
        loadComponent: () => import('./components/user-achievements/user-achievements.component').then(m => m.UserAchievementsComponent)
      },
      {
        path: 'inventory',
        loadComponent: () => import('./components/user-inventory/user-inventory.component').then(m => m.UserInventoryComponent)
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserRoutingModule {}
