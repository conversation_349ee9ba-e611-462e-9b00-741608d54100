// @import '@ctrl/ngx-emoji-mart/picker';
@tailwind base;
@tailwind components;
@tailwind utilities;
:root,
html,
body {
  font-family: 'Roboto', sans-serif;
  scroll-behavior: smooth !important;
  overflow-x: hidden;  
}

.wrapper-container
{
  @apply bg-white dark:bg-dark-bg dark:text-light-text;
}
main {
  @apply min-h-[80vh] flex flex-col bg-white dark:bg-dark-bg;
}

// Define custom scrollbar styles
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  // background: #f1f1f1;
  background-color: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
  @apply bg-neutral-200 dark:bg-neutral-600 rounded-lg cursor-pointer;
  // border-radius: 6px;
  // background-color: rgba(0, 0, 0, 0.2);
  // cursor: pointer;
}


.scrollbar-style-1::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}


.block-title {
  @apply text-xl uppercase font-extrabold leading-3 flex min-h-full px-2 text-center items-center justify-center text-gray-700 dark:text-light-text mt-0.5;
}



// .block-title {
//   @apply  text-lg uppercase font-bold flex min-h-full px-2 text-center items-center justify-center;
// }

.card-v1-container {
  @apply border border-neutral-200 dark:border-neutral-700 shadow-none sm:shadow-md relative h-full flex flex-col rounded-t-lg rounded-b-md overflow-hidden ;
}


.comic-title {
  @apply font-semibold line-clamp-2 text-sm;
}
