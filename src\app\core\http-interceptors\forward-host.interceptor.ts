// src/app/interceptors/forward-host.interceptor.ts
import { isPlatformServer } from '@angular/common';
import { HttpE<PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Inject, Injectable, Optional, PLATFORM_ID, REQUEST } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable()
export class ForwardHostInterceptor implements HttpInterceptor {
  constructor(
    @Inject(PLATFORM_ID) private platformId: object,
    @Optional() @Inject(REQUEST) private request: Request
  ) {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    // Chỉ hoạt động trên server
    if (isPlatformServer(this.platformId) && this.request) {
      const realHost = this.request.headers.get('host');
      if (realHost) {
        const modified = req.clone({
          setHeaders: {
            'X-Real-Host': realHost
          }
        });
        return next.handle(modified);
      }
    }

    return next.handle(req);
  }
}
