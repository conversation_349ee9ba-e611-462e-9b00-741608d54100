<div class="emoji-container">
  <!-- Emoji Pack Selector Header -->
  <div class="emoji-header">
    <div
      *ngFor="let emoji of visibleEmojiPacks(); trackBy: trackByEmojiId"
      class="emoji-pack-item"
      [class.active]="activate() === emoji.id"
      [class.inactive]="activate() !== emoji.id"
      (click)="selectPacket(emoji.id)"
    >
      <img
        class="emoji-pack-image"
        [src]="emoji.img"
        [alt]="emoji.describe"
        loading="lazy"
      />
    </div>

    <button
      (click)="togglePopover()"
      class="expand-button"
      [attr.aria-expanded]="isVisible()"
      aria-label="Xem thêm emoji packs"
    >
      <svg
        class="expand-icon"
        [class.rotated]="isVisible()"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path stroke="none" d="M0 0h24v24H0z" />
        <polyline points="6 9 12 15 18 9" />
      </svg>
    </button>
  </div>

  <!-- Hidden Emoji Packs Dropdown -->
  <div
    class="emoji-dropdown"
    [class.hidden]="!isVisible()"
  >
    <div class="emoji-dropdown-content">
      <div
        *ngFor="let emoji of hiddenEmojiPacks(); trackBy: trackByEmojiId"
        class="emoji-dropdown-item"
        (click)="selectPacket(emoji.id)"
      >
        <img
          class="emoji-dropdown-image"
          [src]="emoji.img"
          [alt]="emoji.describe"
          loading="lazy"
        />
        <span class="emoji-dropdown-text">{{ emoji.describe }}</span>
      </div>
    </div>
  </div>

  <!-- Emoji Grid -->
  <div class="emoji-grid">
    <ng-container *ngIf="emojiList().length > 0">
      <div
        *ngFor="let emojiIndex of emojiList(); trackBy: trackByEmojiIndex; let i = index"
        class="emoji-item"
        (click)="handleEmojiClick(emojiIndex)"
      >
        <img
          class="emoji-image"
          [src]="getEmojiPath(activate(), emojiIndex)"
          [alt]="'Emoji ' + emojiIndex"
          loading="lazy"
        />
      </div>
    </ng-container>
  </div>
</div>
