import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID, computed, signal } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';

import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic, ComicStatus, Genre, SortType } from '@schema';
import { ComicService } from '@services/comic.service';
import { GenreService } from '@services/genre.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import {
  IFilters,
  genreFiltersOptions,
} from '../../components/utils/constants';
import { combineLatest } from 'rxjs';

@Component({
  selector: 'main[app-genre]',
  templateUrl: './genre.component.html',
  styleUrl: './genre.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GenreComponent extends OptimizedBaseComponent implements OnInit {
  // Component state with signals for optimal performance
  listComics = signal<Comic[]>([]);
  currentGenre = signal<Genre | undefined>(undefined);
  genres = signal<Genre[]>([]);

  // Pagination and filters
  totalpage = signal<number>(0);
  totalResult = signal<number>(0);
  currentPage = 1;
  dataView!: IFilters;

  selectedComic?: Comic;
  isShowDetails = false;

  selectOptions: any = {
    sorts: { value: SortType.LastUpdate, name: 'Mới cập nhật' },
    status: { value: ComicStatus.ALL, name: 'Tất cả' },
  };

  private genreSlug?: string;
  isLoading = signal<boolean>(false);

  queryParams: Params = {};

  // Computed properties for optimal performance
  hasComics = computed(() => this.listComics().length > 0);
  totalComicsCount = computed(() => this.totalpage() * 35);
  genreDescription = computed(() => {
    const genre = this.currentGenre();
    if (!genre) return '';
    return genre.description ||
      `Khám phá kho tàng truyện tranh thể loại ${genre.title} với những câu chuyện hấp dẫn, đa dạng và phong phú. Đọc online miễn phí tại MeTruyenMoi.`;
  });

  // Performance optimizations

  // genres: Genre[] = [];
  

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private urlService: UrlService,
    override cd: ChangeDetectorRef,
    private genreService: GenreService,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);
    this.dataView = {
      status: genreFiltersOptions.status,
      sorts: genreFiltersOptions.sorts,
    };
  }



  ngOnInit(): void {
    this.genreService.getGenres().subscribe((genres: any) => {
      this.genres.set(genres);
    });
    
    const combined$ = combineLatest([
      this.route.paramMap,
      this.route.queryParamMap
    ]);
    combined$.subscribe(([params, queryParams]) => {

      this.genreSlug = params.get('slug') || undefined;
      const page = Number(queryParams.get('page')) || 1;
      const status = Number(queryParams.get('status')) >= 0 ? Number(queryParams.get('status')) : ComicStatus.ALL;
      const sort = Number(queryParams.get('sort')) >= 0 ? Number(queryParams.get('sort')) : SortType.LastUpdate;

      this.currentPage = page;
      this.selectOptions.sorts.value = sort;
      this.selectOptions.status.value = status;

      this.loadGenreInfo();
    });

  }

  /**
   * Load genre information
   */
  private loadGenreInfo(): void {
    if (!this.genreSlug) return;
    this.genreService.getGenreBySlug(this.genreSlug)
      .pipe(this.takeUntilDestroy())
      .subscribe((genre: any) => {
        if (!genre) {
          this.router.navigate(['/404']);
          return;
        }
        this.currentGenre.set(genre);

        // Load comics after genre info is loaded
        const page = this.currentPage;
        const sort = this.selectOptions.sorts.value;
        const status = this.selectOptions.status.value;
        this.performLoadComicsByGenre(page, sort, status);
        this.safeMarkForCheck();
      });
  }

  /**
   * Load comics by genre with filters - debounced for performance
   */
  private performLoadComicsByGenre(page: number, sort: number, status: number): void {
    if (!this.currentGenre() || this.isLoading()) return;

    this.isLoading.set(true);
    this.comicService
      .getComics({
        step: '35',
        genre: this.currentGenre()!.id.toString(),
        page: page.toString(),
        sort: sort.toString(),
        status: status.toString(),
      })
      .pipe(this.takeUntilDestroy())
      .subscribe((res: any) => {
        this.totalpage.set(res.data.totalpage);
        this.totalResult.set(res.data.totalResult || 0);
        this.listComics.set(res.data.comics || []);

        // Show details of the first comic if not already selected
        if (!this.selectedComic && this.listComics().length > 0) {
          this.showDetails(this.listComics()[0]);
        }
        
        this.isLoading.set(false);
        this.safeMarkForCheck();
        this.setupSeo();
      });
  }


  /**
   * Handle sort option change
   */
  onSortOptionChange(value: number): void {
    this.selectOptions.sorts.value = value;
    this.queryParams = {
      ...this.queryParams,
      sort: value,
      page: 1 // Reset to first page when changing sort
    };
    this.router.navigate([], {
      queryParams: this.queryParams,
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  /**
   * Handle status option change
   */
  onStatusOptionChange(value: number): void {
    this.selectOptions.status.value = value;
    this.queryParams = {
      ...this.queryParams,
      status: value,
      page: 1 // Reset to first page when changing status
    };
    this.router.navigate([], {
      queryParams: this.queryParams,
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  /**
   * Show comic details
   */
  showDetails(comic: Comic): void {
    this.selectedComic = comic;
  }


  /**
   * Setup SEO for genre page
   */
  private setupSeo(): void {
    const genre = this.currentGenre();
    if (!genre) return;
    this.seoService.setGenreSEO(genre, this.listComics(), this.currentPage);
  }

  /**
   * Track by function for comic list optimization
   */
  trackByComicId = (index: number, comic: Comic): number => comic.id;

  /**
   * Track by function for genre list optimization
   */
  trackByGenreId = (index: number, genre: Genre): number => genre.id;
}
