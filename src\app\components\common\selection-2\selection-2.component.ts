import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, ContentChild, EventEmitter, Input, Output, TemplateRef } from '@angular/core';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { IOption } from 'src/app/dataSource/schema/IOption';

@Component({
  selector: 'div[app-selection-2]',
  standalone: true,
  templateUrl: './selection-2.component.html',
  styleUrl: './selection-2.component.scss',
  imports: [CommonModule, ClickOutsideDirective],
  // changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Selection2Component {

  isDropdownOpen = false;  // Biến điều khiển trạng thái dropdown
  @Input()
  selectedValue?: any;  // Lưu trữ giá trị đã chọn
  @Input() direct: 'up' | 'down' | 'auto' = 'down'; // Hướng mở dropdown
  @Output()
  selectedValueChange = new EventEmitter<any>();
  @Input()
  options: IOption[] = []  // Danh sách tùy chọn
  @Input()
  placeholder = 'Chọn';
  @Input()
  size: 'small' | 'medium' = 'medium';
  selectedIdx = 0;
  @Input() isFit = true; // Nếu true, dropdown sẽ chiếm toàn bộ chiều rộng của phần tử cha
  @ContentChild('customBtn') customBtn?: TemplateRef<any>;
  @Input()
  checkmark = false;
  constructor(private cd: ChangeDetectorRef) { };
  // Hàm để toggle (mở hoặc đóng) dropdown
  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  ngOnInit() {
    let idx = this.options.findIndex((option) => option.value === this.selectedValue);
    this.selectedIdx = idx
  }

  // Hàm để chọn một option
  selectOption(option: IOption) {
    this.selectedValue = option.value;
    this.selectedIdx = this.options.findIndex((option) => option.value === this.selectedValue);
    this.isDropdownOpen = false;  // Đóng dropdown sau khi chọn
    option.selected = true;
    this.selectedValueChange.emit(this.selectedValue);
  }

  closeDropdown() {
    this.isDropdownOpen = false;
  }

  ngOnChanges() {
    let idx = this.options.findIndex((option) => option.value === this.selectedValue);
    this.selectedIdx = idx    
    this.cd.markForCheck();

  }
}
