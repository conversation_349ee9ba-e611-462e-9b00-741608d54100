import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  On<PERSON><PERSON>roy,
  OnInit,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SeoService } from '@services/seo.service';
import { SyncCredentials, SyncSettings, SyncStage } from './models/sync-tracking.models';
import { SyncTrackingService } from './services/sync-tracking.service';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'main[app-sync-tracking]',
  templateUrl: './sync-tracking.component.html',
  styleUrl: './sync-tracking.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false
})
export class SyncTrackingComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  // Component state
  private readonly _currentStep = signal<'form' | 'progress' | 'results'>('form');
  private readonly _isLoading = signal(false);
  protected override readonly destroy$ = new Subject<void>();

  // Computed properties
  readonly currentStep = computed(() => this._currentStep());
  readonly isLoading = computed(() => this._isLoading());
  
  readonly showForm = computed(() => this.currentStep() === 'form');
  readonly showProgress = computed(() => this.currentStep() === 'progress');
  readonly showResults = computed(() => this.currentStep() === 'results');

  readonly pageTitle = computed(() => {
    const titles = {
      form: 'Đồng bộ theo dõi truyện',
      progress: 'Đang đồng bộ...',
      results: 'Kết quả đồng bộ'
    };
    return titles[this.currentStep()];
  });

  readonly pageSubtitle = computed(() => {
    const subtitles = {
      form: 'Đồng bộ danh sách theo dõi giữa NetTruyen và TruyenQQ',
      progress: 'Vui lòng đợi trong khi hệ thống đồng bộ dữ liệu',
      results: 'Xem chi tiết kết quả đồng bộ và thống kê'
    };
    return subtitles[this.currentStep()];
  });

  constructor(
    private syncService: SyncTrackingService,
    private seoService: SeoService,
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.setupSEO();
    this.subscribeToSyncProgress();
    this.initializePage();
  }

  override ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ===== INITIALIZATION =====

  private initializePage(): void {
    // Reset sync state on page load
    this.syncService.resetSync();
    this._currentStep.set('form');
  }

  // ===== SUBSCRIPTION MANAGEMENT =====

  private subscribeToSyncProgress(): void {
    this.syncService.progress$
      .pipe(takeUntil(this.destroy$))
      .subscribe(progress => {
        // Auto-navigate based on sync stage
        if (progress.stage === SyncStage.IDLE) {
          this._currentStep.set('form');
        } else if (progress.stage === SyncStage.COMPLETED || progress.stage === SyncStage.ERROR) {
          this._currentStep.set('results');
        } else {
          this._currentStep.set('progress');
        }
        
        this.safeMarkForCheck();
      });
  }

  // ===== EVENT HANDLERS =====

  onSyncStart(event: { credentials: SyncCredentials}): void {
    this._isLoading.set(true);
    this._currentStep.set('progress');
    console.log('Starting sync with credentials:', event.credentials);
    
    this.runInBrowser(() => {
      this.syncService.startSync(event.credentials).subscribe({
        next: (result) => {
          // console.log('Sync completed:', result);
          // this._isLoading.set(false);
          // this._currentStep.set('results');
          // this.safeMarkForCheck();
        },
        error: (error) => {
          console.error('Sync failed:', error);
          this._isLoading.set(false);
          this._currentStep.set('results');
          this.safeMarkForCheck();
        }
      });
    });
  }

  onNewSync(): void {
    this.syncService.resetSync();
    this._currentStep.set('form');
    this._isLoading.set(false);
  }

  // ===== NAVIGATION METHODS =====

  goToForm(): void {
    this._currentStep.set('form');
    this.syncService.resetSync();
  }

  goToProgress(): void {
    if (this.syncService.isSyncInProgress()) {
      this._currentStep.set('progress');
    }
  }

  goToResults(): void {
    if (this.syncService.getCurrentProgress().stage === SyncStage.COMPLETED ||
        this.syncService.getCurrentProgress().stage === SyncStage.ERROR) {
      this._currentStep.set('results');
    }
  }

  // ===== UTILITY METHODS =====

  getStepClass(step: 'form' | 'progress' | 'results'): string {
    const current = this.currentStep();
    if (current === step) return 'active';
    
    const stepOrder = ['form', 'progress', 'results'];
    const currentIndex = stepOrder.indexOf(current);
    const stepIndex = stepOrder.indexOf(step);
    
    return currentIndex > stepIndex ? 'completed' : 'pending';
  }

  canNavigateToStep(step: 'form' | 'progress' | 'results'): boolean {
    switch (step) {
      case 'form':
        return true; // Always can go back to form
      case 'progress':
        return this.syncService.isSyncInProgress();
      case 'results':
        const stage = this.syncService.getCurrentProgress().stage;
        return stage === SyncStage.COMPLETED || stage === SyncStage.ERROR;
      default:
        return false;
    }
  }

  // ===== SEO AND META =====

  private setupSEO(): void {
    // Use the new comprehensive SEO method
    this.seoService.setSyncTrackingSEO();
  }

  private updatePageMeta(): void {
    // This method can be removed as SEO is now handled in setupSEO
    // Keeping for backward compatibility if needed elsewhere
  }
}
