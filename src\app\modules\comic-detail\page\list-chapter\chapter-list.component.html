<div class="chapter-panel">
  <span class="chapter-title">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height="20"
      width="20"
      fill="currentColor"
      viewBox="0 0 512 512"
    >
      <path
        d="M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z"
      />
    </svg>
    <p class="chapter-title-text"><PERSON><PERSON> s<PERSON>ch ch<PERSON></p>
  </span>
  <div class="chapter-controls">
    <div class="chapter-search-container">
      <div class="chapter-search-icon">
        <svg
          class="chapter-search-svg"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 20 20"
        >
          <path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
          />
        </svg>
      </div>
      <input
        type="search"
        maxlength="255"
        id="chapter-search"
        (input)="onSearchChapter($event)"
        class="chapter-search-input"
        placeholder="Tìm chương..."
        required
      />
    </div>
    <div class="flex items-center">
      <label
        for="order"
        class="p-0.5 rounded border border-gray-400 hover:bg-gray-100 bg-white cursor-pointer"
      >
        <input
          class="hidden peer/order"
          type="text"
          id="order"
          type="checkbox"
          [checked]="asc"
          (change)="onOrderChange($event)"
        />
        <svg
          class="size-4 peer-checked/order:hidden"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
          <g id="SVGRepo_iconCarrier">
            <g id="Edit / Sort_Descending">
              <path
                id="Vector"
                d="M4 17H16M4 12H13M4 7H10M18 13V5M18 5L21 8M18 5L15 8"
                stroke="#000000"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </g>
          </g>
        </svg>
        <svg
          class="size-4 hidden peer-checked/order:block"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
          <g id="SVGRepo_iconCarrier">
            <g id="Edit / Sort_Ascending">
              <path
                id="Vector"
                d="M4 17H10M4 12H13M18 11V19M18 19L21 16M18 19L15 16M4 7H16"
                stroke="#000000"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </g>
          </g>
        </svg>
      </label>
      <div
        app-selection
        [options]="options"
        [value]="curOptioneValue"
        (onChange)="onChangeSelection($event)"
        class="chapter-selection"
      ></div>
    </div>
  </div>
</div>
<div *ngIf="preLoadChapters.length === 0 && !isChapterLoading" class="chapter-not-found">
  Không tìm thấy chương ...
</div>

<div class="chapter-list-container overflow-hidden min-h-32">
  <div
    *ngIf="!isBrowser; else loadedContent"
    class="grid grid-cols-2 sm:grid-cols-3 xl:grid-cols-4 w-full mr-2"
  >
    <a
      *ngFor="let item of preLoadChapters; let i = index"
      [routerLink]="getChapterUrl(comic!, item)"
    >
      <div class="chapter-item">
        <div class="chapter-item-content">
          <p class="chapter-item-title">Chapter {{ item.slug }}</p>
        </div>
        <div class="chapter-item-date">
          <div class="chapter-item-date-text">
            {{ item.updateAt | dateAgo }}
          </div>
        </div>
      </div>
    </a>
  </div>

  <ng-template #loadedContent>
    <div class="chapter-loading" *ngIf="isChapterLoading">
      <div app-spinner [sizeSpinner]="'40'"></div>
    </div>
    <div
      app-loop-scroll
      #LoopScroll
      class="flex w-full"
      [allitems]="preLoadChapters"
      [gridSize]="gridSize"
      [preloadItemCount]="40"
      [itemHeight]="56"
      (onChange)="onScrollChange($event)"
    >
      <ng-template #ItemTemplate let-item="item;">
        <a [title]="item.title" [routerLink]="getChapterUrl(comic!, item)">
          <div class="chapter-item">
            <div class="chapter-item-content">
              <p class="chapter-item-title">Chapter {{ item.slug }}</p>
            </div>
            <div class="chapter-item-date">
              <div class="chapter-item-date-text">
                {{ item.updateAt | dateAgo }}
              </div>
            </div>
          </div>
        </a>
      </ng-template>
    </div>
  </ng-template>
</div>
